#!/bin/bash

# AI Recruiter Pro - Production Deployment Script

set -e

echo "🚀 Starting AI Recruiter Pro deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed (optional for containerized deployment)"
    fi
    
    print_success "Dependencies check completed"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm ci --only=production
    print_success "Dependencies installed"
}

# Generate Prisma client
setup_database() {
    print_status "Setting up database..."
    
    # Generate Prisma client
    npx prisma generate
    
    # Create database directories
    mkdir -p db logs public/uploads
    
    # Push database schema
    npx prisma db push
    
    # Seed database if needed
    if [ "$1" = "--seed" ]; then
        print_status "Seeding database..."
        npm run db:seed
    fi
    
    print_success "Database setup completed"
}

# Build the application
build_application() {
    print_status "Building application..."
    npm run build
    print_success "Application built successfully"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    if npm run test --if-present; then
        print_success "All tests passed"
    else
        print_warning "Tests failed or not configured"
    fi
}

# Security checks
security_checks() {
    print_status "Running security checks..."
    
    # Check for security vulnerabilities
    npm audit --audit-level moderate
    
    # Check environment variables
    if [ ! -f ".env.production" ]; then
        print_warning ".env.production file not found"
        print_status "Creating .env.production from template..."
        cp .env.example .env.production
        print_warning "Please update .env.production with your production values"
    fi
    
    print_success "Security checks completed"
}

# Start the application
start_application() {
    print_status "Starting application..."
    
    if [ "$1" = "--docker" ]; then
        print_status "Starting with Docker..."
        docker-compose up -d
        print_success "Application started with Docker"
    else
        print_status "Starting with Node.js..."
        NODE_ENV=production npm start &
        print_success "Application started with Node.js"
    fi
}

# Health check
health_check() {
    print_status "Performing health check..."
    
    # Wait for application to start
    sleep 10
    
    # Check if application is responding
    if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
        print_success "Application is healthy and responding"
    else
        print_error "Application health check failed"
        exit 1
    fi
}

# Main deployment function
deploy() {
    local DOCKER_MODE=false
    local SEED_DB=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --docker)
                DOCKER_MODE=true
                shift
                ;;
            --seed)
                SEED_DB=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --docker    Deploy using Docker"
                echo "  --seed      Seed the database with sample data"
                echo "  --help      Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    print_status "Starting deployment process..."
    
    check_dependencies
    install_dependencies
    
    if [ "$SEED_DB" = true ]; then
        setup_database --seed
    else
        setup_database
    fi
    
    build_application
    run_tests
    security_checks
    
    if [ "$DOCKER_MODE" = true ]; then
        start_application --docker
    else
        start_application
    fi
    
    health_check
    
    print_success "🎉 Deployment completed successfully!"
    print_status "Application is running at: http://localhost:3000"
    print_status "Health check endpoint: http://localhost:3000/api/health"
}

# Run deployment
deploy "$@"
