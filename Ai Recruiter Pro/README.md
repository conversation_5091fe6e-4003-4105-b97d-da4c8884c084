# 🚀 AI Recruiter Pro

A comprehensive, production-ready AI-powered recruitment platform that revolutionizes the hiring process with smart CV parsing, automated candidate assessment, and collaborative interview management.

## ✨ Key Features

### 🤖 AI-Powered Recruitment
- **Smart CV Parsing** - Automatic extraction of candidate information from PDF/DOC files
- **AI Assessment** - Intelligent candidate scoring using advanced AI algorithms
- **Real-time Collaboration** - Live updates and notifications for interview teams
- **Comprehensive Analytics** - Detailed insights and reporting dashboards

### 🎯 Core Capabilities
- **Bulk CV Processing** - Upload and process multiple CVs simultaneously
- **Collaborative Rating** - Multi-interviewer evaluation system
- **Advanced Search & Filtering** - Find candidates quickly with powerful filters
- **Candidate Profiles** - Detailed candidate views with all relevant information

## 🛠️ Technology Stack

### 🎯 Core Framework
- **⚡ Next.js 15** - The React framework for production with App Router
- **📘 TypeScript 5** - Type-safe JavaScript for better developer experience
- **🎨 Tailwind CSS 4** - Utility-first CSS framework for rapid UI development

### 🧩 UI Components & Styling
- **🧩 shadcn/ui** - High-quality, accessible components built on Radix UI
- **🎯 Lucide React** - Beautiful & consistent icon library
- **🌈 Framer Motion** - Production-ready motion library for React
- **📊 Recharts** - Advanced charting library for analytics

### 🗄️ Database & Backend
- **🗄️ Prisma** - Next-generation Node.js and TypeScript ORM
- **🗃️ SQLite** - Lightweight, serverless database (production-ready)
- **🔌 Socket.IO** - Real-time bidirectional event-based communication

### 🤖 AI & Processing
- **🧠 Z.ai SDK** - Advanced AI processing for CV analysis
- **📄 PDF/DOC Parser** - Extract text from various document formats
- **🔍 Smart Extraction** - Intelligent data extraction using regex and AI

### 🔒 Security & Validation
- **✅ Zod** - TypeScript-first schema validation
- **🛡️ Rate Limiting** - API protection against abuse
- **🔐 Input Sanitization** - Secure data handling and validation

## 🚀 Quick Start

### Prerequisites
- **Node.js 18+** - [Download here](https://nodejs.org/)
- **npm** - Package manager (comes with Node.js)
- **Z.ai API Key** - [Get your API key](https://z.ai)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd ai-recruiter-pro
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your configuration values:
   ```env
   DATABASE_URL="file:./db/dev.db"
   Z_AI_API_KEY="your-z-ai-api-key"
   NEXTAUTH_SECRET="your-secret-key"
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   npm run db:seed  # Optional: Add sample data
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to access the application.

## 🏭 Production Deployment

### Automated Deployment

Use the included deployment script for easy production setup:

```bash
# Basic deployment
./deploy.sh

# Deploy with Docker
./deploy.sh --docker

# Deploy with sample data
./deploy.sh --seed
```

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Set up production environment**
   ```bash
   cp .env.example .env.production
   # Edit .env.production with production values
   ```

3. **Start the production server**
   ```bash
   NODE_ENV=production npm start
   ```

### Docker Deployment

1. **Build the Docker image**
   ```bash
   docker build -t ai-recruiter-pro .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

## 🤖 Powered by Z.ai

This application leverages [Z.ai](https://chat.z.ai) for advanced AI capabilities:

- **🧠 Smart CV Analysis** - Intelligent parsing and candidate assessment
- **📊 Predictive Scoring** - AI-powered candidate ranking and recommendations
- **🔍 Intelligent Search** - Advanced candidate matching and filtering
- **📈 Analytics Insights** - AI-driven recruitment analytics and trends

## 📚 API Documentation

### Core Endpoints

#### Candidates
- `GET /api/candidates` - List all candidates with pagination and filtering
- `POST /api/candidates` - Create a new candidate
- `GET /api/candidates/[id]` - Get candidate details
- `PUT /api/candidates/[id]` - Update candidate information
- `DELETE /api/candidates/[id]` - Delete a candidate

#### CV Processing
- `POST /api/parse-cv` - Upload and parse CV files (PDF/DOC)
- `POST /api/ai-analysis` - Get AI analysis for a candidate

#### Analytics
- `GET /api/analytics` - Get comprehensive recruitment analytics
- `GET /api/health` - Application health check

#### Ratings & Reviews
- `POST /api/ratings` - Submit candidate ratings
- `GET /api/ratings/[candidateId]` - Get ratings for a candidate

### Example Usage

#### Upload and Parse CV
```javascript
const formData = new FormData();
formData.append('file', cvFile);

const response = await fetch('/api/parse-cv', {
  method: 'POST',
  body: formData
});

const result = await response.json();
```

#### Get Analytics Data
```javascript
const analytics = await fetch('/api/analytics').then(res => res.json());
console.log(analytics.data.summary); // Overview statistics
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `DATABASE_URL` | Database connection string | Yes | `file:./db/dev.db` |
| `Z_AI_API_KEY` | Z.ai API key for AI features | Yes | - |
| `NEXTAUTH_SECRET` | Secret for authentication | Yes | - |
| `NEXTAUTH_URL` | Application URL | No | `http://localhost:3000` |
| `UPLOAD_DIR` | Directory for file uploads | No | `./public/uploads` |
| `MAX_FILE_SIZE` | Maximum file size in bytes | No | `10485760` (10MB) |

### Database Configuration

The application uses SQLite by default for simplicity, but can be configured for PostgreSQL, MySQL, or other databases supported by Prisma.

To switch to PostgreSQL:
1. Update `DATABASE_URL` in your environment file
2. Update the `provider` in `prisma/schema.prisma`
3. Run `npx prisma db push`

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router pages
├── components/          # Reusable React components
│   └── ui/             # shadcn/ui components
├── hooks/              # Custom React hooks
└── lib/                # Utility functions and configurations
```

## 🎨 Available Features & Components

This scaffold includes a comprehensive set of modern web development tools:

### 🧩 UI Components (shadcn/ui)
- **Layout**: Card, Separator, Aspect Ratio, Resizable Panels
- **Forms**: Input, Textarea, Select, Checkbox, Radio Group, Switch
- **Feedback**: Alert, Toast (Sonner), Progress, Skeleton
- **Navigation**: Breadcrumb, Menubar, Navigation Menu, Pagination
- **Overlay**: Dialog, Sheet, Popover, Tooltip, Hover Card
- **Data Display**: Badge, Avatar, Calendar

### 📊 Advanced Data Features
- **Tables**: Powerful data tables with sorting, filtering, pagination (TanStack Table)
- **Charts**: Beautiful visualizations with Recharts
- **Forms**: Type-safe forms with React Hook Form + Zod validation

### 🎨 Interactive Features
- **Animations**: Smooth micro-interactions with Framer Motion
- **Drag & Drop**: Modern drag-and-drop functionality with DND Kit
- **Theme Switching**: Built-in dark/light mode support

### 🔐 Backend Integration
- **Authentication**: Ready-to-use auth flows with NextAuth.js
- **Database**: Type-safe database operations with Prisma
- **API Client**: HTTP requests with Axios + TanStack Query
- **State Management**: Simple and scalable with Zustand

### 🌍 Production Features
- **Internationalization**: Multi-language support with Next Intl
- **Image Optimization**: Automatic image processing with Sharp
- **Type Safety**: End-to-end TypeScript with Zod validation
- **Essential Hooks**: 100+ useful React hooks with ReactUse for common patterns

## 🤝 Get Started with Z.ai

1. **Clone this scaffold** to jumpstart your project
2. **Visit [chat.z.ai](https://chat.z.ai)** to access your AI coding assistant
3. **Start building** with intelligent code generation and assistance
4. **Deploy with confidence** using the production-ready setup

---

Built with ❤️ for the developer community. Supercharged by [Z.ai](https://chat.z.ai) 🚀
