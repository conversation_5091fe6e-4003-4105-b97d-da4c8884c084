# AI Recruiter Pro - User Guide

## Overview

AI Recruiter Pro is a comprehensive recruitment platform that streamlines the hiring process through AI-powered CV analysis, collaborative candidate evaluation, and advanced analytics. This guide will help you get started and make the most of the platform's features.

## Getting Started

### Accessing the Platform

1. Open your web browser and navigate to the application URL
2. The dashboard will load with the main navigation tabs
3. No login is required in the current version

### Dashboard Overview

The main dashboard consists of four primary tabs:

- **📋 Candidates**: Manage candidate profiles and applications
- **👥 Interviewers**: Manage your interview team
- **📊 Criteria**: Define evaluation criteria
- **📈 Analytics**: View comprehensive recruitment analytics

## Managing Candidates

### Adding Candidates

#### Method 1: Manual Entry

1. Navigate to the **Candidates** tab
2. Click the **"Add Candidate"** button
3. Fill in the candidate information:
   - **Name**: Full name of the candidate
   - **Email**: Primary email address
   - **Phone**: Contact phone number (optional)
4. Click **"Add Candidate"** to save

#### Method 2: CV Upload

1. Click the **"Upload CV"** button
2. Select a PDF or DOC file (max 10MB)
3. The system will automatically:
   - Extract text from the document
   - Parse candidate information
   - Create a candidate profile
   - Generate AI analysis

### Viewing Candidate Details

1. Click the **"View"** button on any candidate card
2. The detailed modal will show:
   - **Overview**: Personal and professional information
   - **AI Analysis**: Comprehensive AI assessment
   - **Ratings**: Interviewer evaluations
   - **Documents**: Uploaded files and extracted text

### CV Processing

#### Supported Formats
- PDF files (.pdf)
- Microsoft Word documents (.doc, .docx)
- Maximum file size: 10MB

#### What Gets Extracted
- Personal information (name, email, phone)
- Professional experience
- Skills and technologies
- Education background
- Current role and summary
- Contact information (LinkedIn, GitHub)

### AI Analysis

The AI system automatically analyzes candidates and provides:

- **Overall Score**: 0-10 rating based on multiple criteria
- **Recommendation**: Hire/No Hire/Maybe recommendation
- **Career Level**: Junior/Mid/Senior assessment
- **Detailed Criteria Scores**: Breakdown by evaluation criteria
- **Strengths & Weaknesses**: Key insights
- **Summary**: Comprehensive analysis narrative

## Managing Interviewers

### Adding Interviewers

1. Navigate to the **Interviewers** tab
2. Click **"Add Interviewer"**
3. Enter interviewer details:
   - **Name**: Full name
   - **Email**: Email address
   - **Role**: Job title or role (optional)
4. Click **"Add Interviewer"** to save

### Interviewer Roles

Interviewers can evaluate candidates based on defined criteria. Each interviewer can:
- Rate candidates on multiple criteria
- Add detailed notes and feedback
- View AI analysis results
- Collaborate with other team members

## Evaluation Criteria

### Setting Up Criteria

1. Navigate to the **Criteria** tab
2. Click **"Add Criteria"**
3. Define the criteria:
   - **Name**: Criteria name (e.g., "Technical Skills")
   - **Description**: Detailed description
   - **Weight**: Importance factor (1-5)
   - **Max Score**: Maximum possible score (typically 10)
4. Click **"Add Criteria"** to save

### Default Criteria

The system comes with pre-configured criteria:
- **Technical Skills** (Weight: 2)
- **Communication Skills** (Weight: 1.5)
- **Problem Solving** (Weight: 2)
- **Team Collaboration** (Weight: 1.5)
- **Cultural Fit** (Weight: 1)
- **Experience & Background** (Weight: 2)

### Rating Candidates

1. Click the **"Rate"** button on a candidate card
2. Select an interviewer from the dropdown
3. For each criteria:
   - Assign a score (0 to max score)
   - Add optional notes and feedback
4. Click **"Submit Ratings"** to save

## Analytics Dashboard

### Overview Metrics

The analytics dashboard provides comprehensive insights:

#### Summary Statistics
- Total candidates in the system
- Candidates with uploaded CVs
- Candidates with AI scores
- Candidates with human ratings
- Active interviewers and criteria

#### Score Distribution
- Visual breakdown of candidate scores
- Distribution across score ranges (0-2, 2-4, 4-6, 6-8, 8-10)
- Pie chart visualization

#### Top Performers
- Highest-scoring candidates
- Bar chart showing AI scores
- Quick identification of strong candidates

#### Criteria Performance
- Average scores per criteria
- Number of ratings per criteria
- Identification of strong/weak areas

#### Interviewer Activity
- Ratings given per interviewer
- Candidates evaluated per interviewer
- Team participation metrics

#### Skills Analysis
- Most common skills across candidates
- Skill frequency and trends
- Technology stack insights

#### Experience Distribution
- Breakdown by experience levels
- Career stage analysis
- Seniority distribution

#### Conversion Funnel
- Application to CV upload rate
- CV to AI analysis rate
- AI analysis to human review rate
- Overall conversion metrics

## Best Practices

### For Recruiters

1. **Consistent Criteria**: Use the same evaluation criteria for all candidates in a role
2. **Multiple Reviewers**: Have multiple interviewers rate each candidate for balanced assessment
3. **Document Feedback**: Always add notes when rating candidates
4. **Regular Reviews**: Check analytics regularly to identify trends and improvements

### For Interviewers

1. **Thorough Review**: Read both CV and AI analysis before rating
2. **Objective Scoring**: Use the full score range (0-10) appropriately
3. **Detailed Notes**: Provide specific feedback and examples
4. **Timely Evaluation**: Complete ratings promptly after interviews

### For CV Processing

1. **Quality Files**: Upload clear, well-formatted CV files
2. **Standard Formats**: Use PDF or DOC formats for best results
3. **Complete Information**: Ensure CVs contain comprehensive candidate information
4. **File Naming**: Use descriptive filenames (e.g., "John_Doe_CV.pdf")

## Troubleshooting

### Common Issues

#### CV Upload Problems
- **File too large**: Ensure file is under 10MB
- **Unsupported format**: Use PDF, DOC, or DOCX files only
- **Processing failed**: Check file isn't corrupted or password-protected

#### AI Analysis Issues
- **No analysis generated**: Ensure CV text was extracted successfully
- **Low confidence scores**: May indicate poor CV quality or formatting
- **Missing information**: AI works best with complete, well-structured CVs

#### Rating Problems
- **Can't submit ratings**: Ensure all required fields are completed
- **Ratings not saving**: Check network connection and try again
- **Missing criteria**: Verify evaluation criteria are properly configured

### Getting Help

If you encounter issues:

1. **Check System Health**: Visit `/api/health` to verify system status
2. **Review Logs**: Check browser console for error messages
3. **Refresh Page**: Sometimes a simple refresh resolves temporary issues
4. **Contact Support**: Reach out to your system administrator

## Advanced Features

### Real-time Collaboration

The platform supports real-time updates:
- Live notifications when new candidates are added
- Real-time rating updates
- Collaborative evaluation sessions

### Bulk Operations

- Upload multiple CVs simultaneously
- Batch candidate processing
- Bulk rating assignments

### Export Capabilities

- Export candidate data
- Generate evaluation reports
- Download analytics summaries

### Search and Filtering

- Search candidates by name or email
- Filter by CV availability
- Filter by AI score ranges
- Sort by various criteria

## Security and Privacy

### Data Protection

- All uploaded files are securely stored
- Personal information is protected
- Access logs are maintained

### File Security

- File type validation prevents malicious uploads
- Size limits prevent system abuse
- Secure file storage and access

### Rate Limiting

- API requests are rate-limited to prevent abuse
- Fair usage policies are enforced
- System performance is protected

## Tips for Success

1. **Start Small**: Begin with a few candidates to familiarize yourself with the system
2. **Train Your Team**: Ensure all interviewers understand the rating system
3. **Regular Maintenance**: Periodically review and update evaluation criteria
4. **Monitor Analytics**: Use insights to improve your recruitment process
5. **Feedback Loop**: Continuously refine criteria based on hiring outcomes

## Support and Resources

- **API Documentation**: Technical details for developers
- **Deployment Guide**: Instructions for system administrators
- **User Community**: Connect with other users
- **Feature Requests**: Submit suggestions for improvements

For additional support, contact your system administrator or check the project documentation.
