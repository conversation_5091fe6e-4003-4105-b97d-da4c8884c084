# AI Recruiter Pro - Deployment Guide

## Overview

This guide covers various deployment options for AI Recruiter Pro, from development setup to production deployment on different platforms.

## Prerequisites

- **Node.js 18+** - [Download](https://nodejs.org/)
- **npm** - Comes with Node.js
- **Git** - For version control
- **Z.ai API Key** - [Get your key](https://z.ai)

## Environment Setup

### 1. Development Environment

```bash
# Clone the repository
git clone <your-repo-url>
cd ai-recruiter-pro

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Set up database
npx prisma generate
npx prisma db push
npm run db:seed  # Optional: Add sample data

# Start development server
npm run dev
```

### 2. Production Environment Variables

Create `.env.production` with the following variables:

```env
# Database
DATABASE_URL="file:./db/production.db"

# Authentication
NEXTAUTH_SECRET="your-super-secret-key-change-this"
NEXTAUTH_URL="https://your-domain.com"

# Socket.IO
NEXT_PUBLIC_SOCKET_URL="https://your-domain.com"

# AI Service
Z_AI_API_KEY="your-production-z-ai-api-key"
Z_AI_BASE_URL="https://api.z.ai"

# File Upload
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE="10485760"

# Security
CORS_ORIGIN="https://your-domain.com"
RATE_LIMIT_WINDOW_MS="60000"
RATE_LIMIT_MAX_REQUESTS="100"

# Logging
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# Environment
NODE_ENV="production"
```

## Deployment Options

### Option 1: Automated Deployment Script

The easiest way to deploy is using the included deployment script:

```bash
# Basic deployment
./deploy.sh

# Deploy with Docker
./deploy.sh --docker

# Deploy with sample data
./deploy.sh --seed

# Get help
./deploy.sh --help
```

### Option 2: Manual Deployment

#### Step 1: Build the Application

```bash
# Install production dependencies
npm ci --only=production

# Generate Prisma client
npx prisma generate

# Build the application
npm run build

# Set up production database
npx prisma db push
```

#### Step 2: Start the Production Server

```bash
# Set environment
export NODE_ENV=production

# Start the server
npm start
```

### Option 3: Docker Deployment

#### Using Docker Compose (Recommended)

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

#### Manual Docker Build

```bash
# Build the image
docker build -t ai-recruiter-pro .

# Run the container
docker run -d \
  --name ai-recruiter-pro \
  -p 3000:3000 \
  -v $(pwd)/db:/app/db \
  -v $(pwd)/public/uploads:/app/public/uploads \
  -v $(pwd)/logs:/app/logs \
  -e NODE_ENV=production \
  -e DATABASE_URL=file:./db/production.db \
  -e Z_AI_API_KEY=your-api-key \
  ai-recruiter-pro
```

## Platform-Specific Deployments

### Vercel

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Configure vercel.json:**
   ```json
   {
     "version": 2,
     "builds": [
       {
         "src": "package.json",
         "use": "@vercel/next"
       }
     ],
     "env": {
       "DATABASE_URL": "@database_url",
       "Z_AI_API_KEY": "@z_ai_api_key",
       "NEXTAUTH_SECRET": "@nextauth_secret"
     }
   }
   ```

3. **Deploy:**
   ```bash
   vercel --prod
   ```

### Netlify

1. **Build settings:**
   - Build command: `npm run build`
   - Publish directory: `.next`

2. **Environment variables:**
   Set all required environment variables in Netlify dashboard.

3. **Deploy:**
   ```bash
   npm run build
   netlify deploy --prod --dir=.next
   ```

### Railway

1. **Install Railway CLI:**
   ```bash
   npm i -g @railway/cli
   ```

2. **Deploy:**
   ```bash
   railway login
   railway init
   railway up
   ```

### DigitalOcean App Platform

1. **Create app.yaml:**
   ```yaml
   name: ai-recruiter-pro
   services:
   - name: web
     source_dir: /
     github:
       repo: your-username/ai-recruiter-pro
       branch: main
     run_command: npm start
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     envs:
     - key: NODE_ENV
       value: production
     - key: DATABASE_URL
       value: file:./db/production.db
   ```

2. **Deploy via dashboard or CLI**

### AWS EC2

1. **Launch EC2 instance** (Ubuntu 20.04 LTS recommended)

2. **Install dependencies:**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y

   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Install PM2 for process management
   sudo npm install -g pm2

   # Install Nginx for reverse proxy
   sudo apt install nginx -y
   ```

3. **Deploy application:**
   ```bash
   # Clone repository
   git clone <your-repo-url>
   cd ai-recruiter-pro

   # Install dependencies and build
   npm ci --only=production
   npm run build

   # Start with PM2
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

4. **Configure Nginx:**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## Database Considerations

### SQLite (Default)

- **Pros**: Simple, no setup required, good for small to medium applications
- **Cons**: Not suitable for high-concurrency scenarios
- **Recommended for**: Development, small production deployments

### PostgreSQL (Recommended for Production)

1. **Update Prisma schema:**
   ```prisma
   generator client {
     provider = "prisma-client-js"
   }

   datasource db {
     provider = "postgresql"
     url      = env("DATABASE_URL")
   }
   ```

2. **Update DATABASE_URL:**
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/ai_recruiter_pro"
   ```

3. **Run migrations:**
   ```bash
   npx prisma db push
   ```

## SSL/HTTPS Setup

### Using Let's Encrypt (Free)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Maintenance

### Health Checks

The application provides a health check endpoint:
```bash
curl https://your-domain.com/api/health
```

### Log Management

Logs are written to `./logs/app.log`. Set up log rotation:

```bash
# Create logrotate config
sudo nano /etc/logrotate.d/ai-recruiter-pro

# Add configuration:
/path/to/ai-recruiter-pro/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload ai-recruiter-pro
    endscript
}
```

### Backup Strategy

1. **Database backup:**
   ```bash
   # SQLite
   cp db/production.db backups/db-$(date +%Y%m%d).db

   # PostgreSQL
   pg_dump ai_recruiter_pro > backups/db-$(date +%Y%m%d).sql
   ```

2. **File uploads backup:**
   ```bash
   tar -czf backups/uploads-$(date +%Y%m%d).tar.gz public/uploads/
   ```

### Performance Optimization

1. **Enable gzip compression** (Nginx):
   ```nginx
   gzip on;
   gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
   ```

2. **Set up caching headers**:
   ```nginx
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

3. **Monitor performance**:
   ```bash
   # Check application metrics
   curl https://your-domain.com/api/health

   # Monitor system resources
   htop
   df -h
   ```

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   lsof -ti:3000 | xargs kill -9
   ```

2. **Database connection issues:**
   ```bash
   # Check database file permissions
   ls -la db/
   
   # Regenerate Prisma client
   npx prisma generate
   ```

3. **File upload issues:**
   ```bash
   # Check upload directory permissions
   chmod 755 public/uploads
   ```

4. **Memory issues:**
   ```bash
   # Check memory usage
   free -h
   
   # Restart application
   pm2 restart ai-recruiter-pro
   ```

### Getting Help

- Check application logs: `tail -f logs/app.log`
- Check system logs: `journalctl -u nginx -f`
- Monitor health endpoint: `curl /api/health`
- Review error messages in browser console

## Security Checklist

- [ ] Environment variables are properly set
- [ ] Database is secured with proper credentials
- [ ] SSL/HTTPS is enabled
- [ ] File upload restrictions are in place
- [ ] Rate limiting is configured
- [ ] Security headers are enabled
- [ ] Regular backups are scheduled
- [ ] Monitoring is set up
- [ ] Log rotation is configured
- [ ] Firewall rules are properly configured

## Support

For additional support:
- Check the [API Documentation](./API.md)
- Review the [User Guide](./USER_GUIDE.md)
- Open an issue on GitHub
- Contact support team
