# AI Recruiter Pro - API Documentation

## Overview

The AI Recruiter Pro API provides comprehensive endpoints for managing candidates, processing CVs, conducting AI analysis, and generating analytics reports. All endpoints return JSON responses and follow RESTful conventions.

## Base URL

```
Development: http://localhost:3000/api
Production: https://your-domain.com/api
```

## Authentication

Currently, the API does not require authentication, but this will be added in future versions. All endpoints are publicly accessible.

## Rate Limiting

- **Window**: 60 seconds
- **Max Requests**: 100 per IP address
- **Headers**: `X-RateLimit-Remaining` indicates remaining requests

## Error Handling

All errors follow a consistent format:

```json
{
  "error": "Error message",
  "statusCode": 400,
  "timestamp": "2025-09-14T21:00:00.000Z",
  "details": {} // Optional additional details
}
```

## Endpoints

### Health Check

#### GET /api/health

Returns the current health status of the application.

**Response:**
```json
{
  "status": "healthy|degraded|unhealthy",
  "timestamp": "2025-09-14T21:00:00.000Z",
  "checks": {
    "database": {
      "status": "pass|warn|fail",
      "message": "Database connection healthy",
      "duration": 5
    },
    "memory": {
      "status": "pass|warn|fail",
      "message": "Memory usage normal",
      "details": {
        "percentage": "45.2%",
        "used": 123456789,
        "total": 273456789
      }
    },
    "disk": {
      "status": "pass|warn|fail",
      "message": "Disk access healthy"
    },
    "api": {
      "status": "pass|warn|fail",
      "message": "API performance normal",
      "details": {
        "errorRate": "2.5%",
        "averageResponseTime": "150ms"
      }
    }
  },
  "stats": {
    "candidates": 25,
    "interviewers": 5,
    "criteria": 8
  },
  "performance": {
    "memory": {
      "used": 123456789,
      "total": 273456789,
      "percentage": 45.2
    },
    "requests": {
      "total": 1250,
      "successful": 1220,
      "failed": 30,
      "averageResponseTime": 150
    }
  },
  "alerts": ["Warning: High memory usage"],
  "version": "1.0.0",
  "environment": "production",
  "uptime": 86400000
}
```

### Candidates

#### GET /api/candidates

Retrieve all candidates with optional filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)
- `search` (string): Search in name and email
- `hasCV` (boolean): Filter by CV availability
- `hasAIScore` (boolean): Filter by AI score availability
- `minScore` (number): Minimum AI score (0-10)
- `maxScore` (number): Maximum AI score (0-10)
- `experience` (string): Filter by experience level
- `sortBy` (string): Sort field (name, email, createdAt, aiScore)
- `sortOrder` (string): Sort order (asc, desc)

**Response:**
```json
{
  "success": true,
  "data": {
    "candidates": [
      {
        "id": "candidate-id",
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "age": 30,
        "experience": "5 years",
        "cvUrl": "/uploads/cv-file.pdf",
        "cvText": "Extracted CV text...",
        "aiScore": 8.5,
        "aiAnalysis": "{\"overallScore\": 8.5, ...}",
        "averageScore": 7.8,
        "createdAt": "2025-09-14T21:00:00.000Z",
        "parsedData": {
          "name": "John Doe",
          "email": "<EMAIL>",
          "skills": ["JavaScript", "React", "Node.js"],
          "education": "Bachelor's in Computer Science",
          "currentRole": "Senior Developer",
          "summary": "Experienced developer...",
          "location": "New York, NY",
          "linkedin": "https://linkedin.com/in/johndoe",
          "github": "https://github.com/johndoe"
        },
        "ratings": [
          {
            "id": "rating-id",
            "score": 8,
            "notes": "Strong technical skills",
            "criteria": {
              "name": "Technical Skills",
              "maxScore": 10
            },
            "interviewer": {
              "name": "Jane Smith",
              "role": "Tech Lead"
            },
            "createdAt": "2025-09-14T21:00:00.000Z"
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "totalCount": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### POST /api/candidates

Create a new candidate.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "age": 30,
  "experience": "5 years",
  "cvText": "Optional CV text",
  "cvUrl": "Optional CV URL",
  "parsedData": {
    "skills": ["JavaScript", "React"],
    "education": "Bachelor's degree",
    "currentRole": "Developer"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "candidate-id",
    "name": "John Doe",
    "email": "<EMAIL>",
    // ... other candidate fields
  },
  "message": "Candidate created successfully"
}
```

### CV Processing

#### POST /api/parse-cv

Upload and parse a CV file.

**Request:**
- Content-Type: `multipart/form-data`
- Field: `file` (PDF or DOC file, max 10MB)

**Response:**
```json
{
  "success": true,
  "data": {
    "filename": "cv-file.pdf",
    "size": 1024000,
    "extractedText": "Full CV text content...",
    "parsedData": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "age": 30,
      "experience": "5 years",
      "skills": ["JavaScript", "React", "Node.js"],
      "education": "Bachelor's in Computer Science",
      "currentRole": "Senior Developer",
      "summary": "Experienced developer with...",
      "location": "New York, NY",
      "linkedin": "https://linkedin.com/in/johndoe",
      "github": "https://github.com/johndoe"
    },
    "confidence": 0.85,
    "processingTime": 1250
  },
  "message": "CV parsed successfully"
}
```

#### POST /api/ai-analysis

Get AI analysis for a candidate.

**Request Body:**
```json
{
  "candidateId": "candidate-id",
  "cvText": "CV text content",
  "jobDescription": "Optional job description for matching"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overallScore": 8.5,
    "recommendation": "Strong Hire",
    "careerLevel": "Senior",
    "summary": "Excellent candidate with strong technical skills...",
    "criteria": [
      {
        "name": "Technical Skills",
        "score": 9,
        "justification": "Strong programming background...",
        "strengths": ["JavaScript", "React", "Problem Solving"]
      }
    ],
    "strengths": ["Technical expertise", "Communication"],
    "weaknesses": ["Limited leadership experience"],
    "fitScore": 85,
    "processingTime": 2500
  }
}
```

### Analytics

#### GET /api/analytics

Get comprehensive recruitment analytics.

**Response:**
```json
{
  "summary": {
    "totalCandidates": 25,
    "candidatesWithCV": 20,
    "candidatesWithAIScore": 18,
    "candidatesWithRatings": 15,
    "totalInterviewers": 5,
    "totalCriteria": 8
  },
  "scoreDistribution": {
    "0-2": 2,
    "2-4": 3,
    "4-6": 5,
    "6-8": 8,
    "8-10": 7
  },
  "topCandidates": [
    {
      "id": "candidate-id",
      "name": "John Doe",
      "aiScore": 9.2,
      "averageScore": 8.8
    }
  ],
  "criteriaPerformance": [
    {
      "id": "criteria-id",
      "name": "Technical Skills",
      "averageScore": 7.5,
      "ratingsCount": 15,
      "weight": 2,
      "maxScore": 10
    }
  ],
  "interviewerActivity": [
    {
      "id": "interviewer-id",
      "name": "Jane Smith",
      "ratingsGiven": 12,
      "candidatesEvaluated": 8
    }
  ],
  "topSkills": [
    {
      "skill": "JavaScript",
      "count": 15
    }
  ],
  "experienceDistribution": {
    "0-2 years": 5,
    "3-5 years": 8,
    "6-10 years": 7,
    "10+ years": 5
  },
  "conversionFunnel": {
    "totalCandidates": 25,
    "withCV": 20,
    "withAIScore": 18,
    "withRatings": 15,
    "conversionRates": {
      "cvUpload": 80,
      "aiAnalysis": 72,
      "humanReview": 60
    }
  }
}
```

## Error Codes

- **400**: Bad Request - Invalid input data
- **401**: Unauthorized - Authentication required
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource not found
- **409**: Conflict - Resource already exists
- **413**: Payload Too Large - File size exceeds limit
- **415**: Unsupported Media Type - Invalid file type
- **422**: Unprocessable Entity - Validation failed
- **429**: Too Many Requests - Rate limit exceeded
- **500**: Internal Server Error - Server error
- **503**: Service Unavailable - System unhealthy

## Security

- All inputs are sanitized to prevent XSS and SQL injection
- File uploads are validated for type and size
- Rate limiting is enforced per IP address
- Security headers are included in all responses
- Request validation prevents malicious payloads

## Performance

- Database queries are optimized with proper indexing
- Response times are monitored and logged
- Memory usage is tracked and alerted
- Caching is implemented where appropriate
- File processing is handled asynchronously
