import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AI Recruiter Pro - Smart Interview Selection System",
  description: "Advanced AI-powered recruitment platform with smart CV parsing, automated candidate assessment, and collaborative interview management.",
  keywords: ["AI recruitment", "CV parsing", "candidate assessment", "interview management", "HR technology", "talent acquisition"],
  authors: [{ name: "AI Recruiter Pro Team" }],
  openGraph: {
    title: "AI Recruiter Pro",
    description: "Smart CV parsing • AI assessment • Collaborative ranking",
    url: "http://localhost:3000",
    siteName: "AI Recruiter Pro",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Recruiter Pro",
    description: "Smart CV parsing • AI assessment • Collaborative ranking",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}
      >
        {children}
        <Toaster />
      </body>
    </html>
  );
}
