"use client";

import { useState, useEffect, useRef } from "react";
import { io, Socket } from "socket.io-client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, Users, Star, TrendingUp, FileText, Plus, Search, Upload as UploadIcon, Star as StarIcon, Brain, Wifi, WifiOff, Bell, File, Award, Target, Zap, Crown, Sparkles, UserCheck, FileCheck, BarChart3, Eye, Download } from "lucide-react";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, PieChart, Pie, Cell, ResponsiveContainer, LineChart, Line } from "recharts";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import { AnalyticsDashboard } from "@/components/analytics-dashboard";
import { CandidateDetailModal } from "@/components/candidate-detail-modal";

interface Candidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  age?: number;
  experience?: string;
  cvUrl?: string;
  cvText?: string;
  aiScore?: number;
  aiAnalysis?: string;
  averageScore?: number;
  createdAt: string;
  parsedData?: {
    name?: string;
    email?: string;
    phone?: string;
    age?: number;
    experience?: string;
    skills?: string[];
    education?: string;
  };
}

interface Interviewer {
  id: string;
  name: string;
  email: string;
  role?: string;
  createdAt: string;
}

interface Criteria {
  id: string;
  name: string;
  description?: string;
  weight: number;
  maxScore: number;
}

interface ActiveInterviewer {
  id: string;
  name: string;
  socketId: string;
  lastSeen: string;
}

interface RealTimeNotification {
  id: string;
  type: 'rating' | 'cv-upload' | 'ai-analysis' | 'candidate-score' | 'cv-parsed';
  message: string;
  timestamp: string;
  data?: any;
}

export default function Home() {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [interviewers, setInterviewers] = useState<Interviewer[]>([]);
  const [criteria, setCriteria] = useState<Criteria[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddCandidateOpen, setIsAddCandidateOpen] = useState(false);
  const [isAddInterviewerOpen, setIsAddInterviewerOpen] = useState(false);
  const [isAddCriteriaOpen, setIsAddCriteriaOpen] = useState(false);
  const [isUploadCVOpen, setIsUploadCVOpen] = useState(false);
  const [isBulkUploadOpen, setIsBulkUploadOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [uploadingCV, setUploadingCV] = useState(false);
  const [parsingCV, setParsingCV] = useState(false);
  const [isRatingOpen, setIsRatingOpen] = useState(false);
  const [ratingCandidate, setRatingCandidate] = useState<Candidate | null>(null);
  const [currentInterviewer, setCurrentInterviewer] = useState<Interviewer | null>(null);
  const [ratings, setRatings] = useState<Record<string, { score: number; notes: string }>>({});
  const [analyzingAI, setAnalyzingAI] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState<any>(null);
  const [isAIAnalysisOpen, setIsAIAnalysisOpen] = useState(false);
  const [newCandidate, setNewCandidate] = useState({ name: "", email: "", phone: "" });
  const [newInterviewer, setNewInterviewer] = useState({ name: "", email: "", role: "" });
  const [newCriteria, setNewCriteria] = useState({ name: "", description: "", weight: 1, maxScore: 10 });
  const [dragActive, setDragActive] = useState(false);
  const [cvFiles, setCvFiles] = useState<File[]>([]);
  const [parsingResults, setParsingResults] = useState<any[]>([]);
  const [isCandidateDetailOpen, setIsCandidateDetailOpen] = useState(false);
  const [detailCandidate, setDetailCandidate] = useState<Candidate | null>(null);
  
  // Real-time collaboration state
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [activeInterviewers, setActiveInterviewers] = useState<ActiveInterviewer[]>([]);
  const [notifications, setNotifications] = useState<RealTimeNotification[]>([]);
  const [currentInterviewerId, setCurrentInterviewerId] = useState<string | null>(null);
  
  const { toast } = useToast();
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    fetchData();
    initializeSocket();
  }, []);

  const initializeSocket = () => {
    if (typeof window !== 'undefined') {
      const newSocket = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000', {
        transports: ['websocket', 'polling']
      });
      
      socketRef.current = newSocket;
      setSocket(newSocket);

      newSocket.on('connect', () => {
        setIsConnected(true);
        console.log('Connected to socket server');
        
        // Auto-join as first interviewer for demo
        if (interviewers.length > 0 && !currentInterviewerId) {
          const firstInterviewer = interviewers[0];
          setCurrentInterviewerId(firstInterviewer.id);
          newSocket.emit('join-interview-session', {
            interviewerId: firstInterviewer.id,
            interviewerName: firstInterviewer.name
          });
        }
      });

      newSocket.on('disconnect', () => {
        setIsConnected(false);
        console.log('Disconnected from socket server');
      });

      // Real-time event handlers
      newSocket.on('active-interviewers', (interviewersList: ActiveInterviewer[]) => {
        setActiveInterviewers(interviewersList);
      });

      newSocket.on('interviewer-joined', (data) => {
        setActiveInterviewers(data.activeInterviewers);
        addNotification({
          id: Date.now().toString(),
          type: 'rating',
          message: `${data.interviewerName} joined the session`,
          timestamp: new Date().toISOString()
        });
        toast({
          title: "Interviewer Joined",
          description: `${data.interviewerName} is now active in the session`,
        });
      });

      newSocket.on('cv-parsed', (data) => {
        addNotification({
          id: Date.now().toString(),
          type: 'cv-parsed',
          message: `CV parsed for ${data.candidateName}: ${data.extractedInfo}`,
          timestamp: data.timestamp
        });
        
        toast({
          title: "CV Parsed Successfully",
          description: `Extracted information from ${data.candidateName}'s CV`,
        });
        
        fetchData(); // Refresh to get updated candidate data
      });

      newSocket.on('ai-analysis-update', (data) => {
        addNotification({
          id: Date.now().toString(),
          type: 'ai-analysis',
          message: `${data.interviewerName} completed AI analysis for ${data.candidateName} (Score: ${data.aiScore}/10)`,
          timestamp: data.timestamp
        });
        
        toast({
          title: "AI Analysis Completed",
          description: `${data.candidateName} received an AI score of ${data.aiScore}/10`,
        });
        
        fetchData(); // Refresh to get updated scores
      });

      // Set up ping interval for presence
      const pingInterval = setInterval(() => {
        if (currentInterviewerId && newSocket.connected) {
          newSocket.emit('ping', { interviewerId: currentInterviewerId });
        }
      }, 30000);

      return () => {
        clearInterval(pingInterval);
        newSocket.disconnect();
      };
    }
  };

  const addNotification = (notification: RealTimeNotification) => {
    setNotifications(prev => [notification, ...prev].slice(0, 20)); // Keep last 20 notifications
  };

  const joinAsInterviewer = (interviewer: Interviewer) => {
    if (socket && socket.connected) {
      setCurrentInterviewerId(interviewer.id);
      socket.emit('join-interview-session', {
        interviewerId: interviewer.id,
        interviewerName: interviewer.name
      });
      toast({
        title: "Joined Session",
        description: `You are now active as ${interviewer.name}`,
      });
    }
  };

  const fetchData = async () => {
    try {
      const [candidatesRes, interviewersRes, criteriaRes] = await Promise.all([
        fetch("/api/candidates"),
        fetch("/api/interviewers"),
        fetch("/api/criteria")
      ]);

      if (candidatesRes.ok) {
        const candidatesData = await candidatesRes.json();
        setCandidates(candidatesData);
      }

      if (interviewersRes.ok) {
        const interviewersData = await interviewersRes.json();
        setInterviewers(interviewersData);
      }

      if (criteriaRes.ok) {
        const criteriaData = await criteriaRes.json();
        setCriteria(criteriaData);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  const filteredCandidates = candidates.filter(candidate =>
    candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    candidate.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedCandidates = [...filteredCandidates].sort((a, b) => 
    (b.aiScore || 0) - (a.aiScore || 0)
  );

  const getScoreColor = (score: number) => {
    if (score >= 8) return "bg-gradient-to-r from-green-400 to-green-600 text-white";
    if (score >= 6) return "bg-gradient-to-r from-yellow-400 to-yellow-600 text-white";
    return "bg-gradient-to-r from-red-400 to-red-600 text-white";
  };

  const getScoreBadge = (score?: number) => {
    if (!score) return <Badge variant="secondary" className="bg-gray-200 text-gray-700">Not Rated</Badge>;
    return (
      <Badge className={`${getScoreColor(score)} font-bold shadow-lg`}>
        {score.toFixed(1)}/10
      </Badge>
    );
  };

  // Prepare data for charts
  const scoreDistributionData = [
    { range: '0-3', count: candidates.filter(c => (c.aiScore || 0) >= 0 && (c.aiScore || 0) < 3).length, color: '#ef4444' },
    { range: '3-6', count: candidates.filter(c => (c.aiScore || 0) >= 3 && (c.aiScore || 0) < 6).length, color: '#f59e0b' },
    { range: '6-8', count: candidates.filter(c => (c.aiScore || 0) >= 6 && (c.aiScore || 0) < 8).length, color: '#3b82f6' },
    { range: '8-10', count: candidates.filter(c => (c.aiScore || 0) >= 8 && (c.aiScore || 0) <= 10).length, color: '#10b981' }
  ];

  const topCandidatesData = sortedCandidates.slice(0, 5).map(candidate => ({
    name: candidate.name.split(' ')[0],
    aiScore: candidate.aiScore || 0,
    fullName: candidate.name
  }));

  const criteriaData = criteria.map(criterion => ({
    name: criterion.name,
    weight: criterion.weight,
    maxScore: criterion.maxScore
  }));

  const chartConfig = {
    aiScore: {
      label: 'AI Score',
      color: '#8b5cf6',
    },
  };

  // Drag and drop handlers
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    const validFiles = files.filter(file => 
      file.type === 'application/pdf' || 
      file.type === 'application/msword' || 
      file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
    
    if (validFiles.length > 0) {
      setCvFiles(validFiles);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => 
      file.type === 'application/pdf' || 
      file.type === 'application/msword' || 
      file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
    
    if (validFiles.length > 0) {
      setCvFiles(validFiles);
    }
  };

  const parseCVWithAI = async (file: File) => {
    setParsingCV(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch("/api/parse-cv", {
        method: "POST",
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to parse CV');
      }

      const result = await response.json();
      
      // Validate parsed data
      if (!result.parsedData || !result.parsedData.name) {
        throw new Error('Could not extract candidate name from CV');
      }

      // Create candidate with parsed data
      const candidateData = {
        name: result.parsedData.name || 'Unknown',
        email: result.parsedData.email || `${result.parsedData.name.toLowerCase().replace(/\s+/g, '.')}@example.com`,
        phone: result.parsedData.phone || '',
        age: result.parsedData.age,
        experience: result.parsedData.experience,
        cvText: result.cvText,
        cvUrl: result.cvUrl,
        parsedData: result.parsedData
      };

      const candidateResponse = await fetch("/api/candidates", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(candidateData)
      });

      if (!candidateResponse.ok) {
        const errorData = await candidateResponse.json();
        throw new Error(errorData.error || 'Failed to create candidate');
      }

      const candidate = await candidateResponse.json();
      setCandidates(prev => [...prev, candidate]);
      
      // Emit real-time event
      if (socket && socket.connected && currentInterviewerId) {
        const currentInterviewer = interviewers.find(i => i.id === currentInterviewerId);
        if (currentInterviewer) {
          socket.emit('cv-parsed', {
            candidateId: candidate.id,
            candidateName: candidate.name,
            extractedInfo: `Name: ${result.parsedData.name}, Email: ${result.parsedData.email}`,
            interviewerName: currentInterviewer.name
          });
        }
      }

      // Auto-run AI analysis
      await runAIAnalysis(candidate.id, result.cvText);
      
      return { success: true, candidate, parsedData: result.parsedData };
    } catch (error) {
      console.error("Error parsing CV:", error);
      toast({
        title: "CV Processing Failed",
        description: error instanceof Error ? error.message : "Failed to process CV",
        variant: "destructive"
      });
      return { success: false, error: error instanceof Error ? error.message : "Failed to parse CV" };
    } finally {
      setParsingCV(false);
    }
  };

  const runAIAnalysis = async (candidateId: string, cvText: string) => {
    setAnalyzingAI(true);
    try {
      const response = await fetch("/api/ai-analysis", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ candidateId, cvText })
      });

      if (response.ok) {
        const result = await response.json();
        
        // Update candidate in list
        setCandidates(prev => prev.map(c => 
          c.id === candidateId 
            ? { ...c, aiScore: result.aiScore, aiAnalysis: JSON.stringify(result.aiAnalysis) }
            : c
        ));

        // Emit real-time event
        if (socket && socket.connected && currentInterviewerId) {
          const currentInterviewer = interviewers.find(i => i.id === currentInterviewerId);
          const candidate = candidates.find(c => c.id === candidateId);
          if (currentInterviewer && candidate) {
            socket.emit('ai-analysis-update', {
              candidateId,
              candidateName: candidate.name,
              aiScore: result.aiScore,
              interviewerName: currentInterviewer.name
            });
          }
        }

        return result;
      }
    } catch (error) {
      console.error("Error running AI analysis:", error);
    } finally {
      setAnalyzingAI(false);
    }
  };

  const handleBulkUpload = async () => {
    const results = [];
    let successCount = 0;
    let failureCount = 0;

    for (const file of cvFiles) {
      const result = await parseCVWithAI(file);
      results.push(result);
      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }
    }
    
    setParsingResults(results);
    setCvFiles([]);
    setIsBulkUploadOpen(false);
    
    toast({
      title: "Bulk Upload Complete",
      description: `Successfully processed ${successCount} CV${successCount !== 1 ? 's' : ''}${failureCount > 0 ? `, ${failureCount} failed` : ''}`,
      variant: failureCount === 0 ? "default" : "destructive"
    });
  };

  const handleUploadCV = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !selectedCandidate) return;

    setUploadingCV(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('candidateId', selectedCandidate.id);

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        
        // Update candidate in the list
        setCandidates(candidates.map(candidate => 
          candidate.id === selectedCandidate.id 
            ? { ...candidate, cvUrl: result.cvUrl, cvText: result.cvText }
            : candidate
        ));
        
        setIsUploadCVOpen(false);
        setSelectedCandidate(null);
        
        // Auto-run AI analysis
        await runAIAnalysis(selectedCandidate.id, result.cvText);
      }
    } catch (error) {
      console.error("Error uploading CV:", error);
    } finally {
      setUploadingCV(false);
    }
  };

  const openUploadDialog = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsUploadCVOpen(true);
  };

  const openRatingDialog = (candidate: Candidate) => {
    setRatingCandidate(candidate);
    if (interviewers.length > 0) {
      setCurrentInterviewer(interviewers[0]);
    }
    
    const initialRatings: Record<string, { score: number; notes: string }> = {};
    criteria.forEach(criterion => {
      initialRatings[criterion.id] = { score: criterion.maxScore / 2, notes: '' };
    });
    setRatings(initialRatings);
    setIsRatingOpen(true);
  };

  const handleRatingChange = (criteriaId: string, score: number, notes: string) => {
    setRatings(prev => ({
      ...prev,
      [criteriaId]: { score, notes }
    }));
  };

  const submitRatings = async () => {
    if (!ratingCandidate || !currentInterviewer) return;

    try {
      const ratingPromises = Object.entries(ratings).map(([criteriaId, rating]) => 
        fetch("/api/ratings", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            score: rating.score,
            notes: rating.notes,
            candidateId: ratingCandidate.id,
            interviewerId: currentInterviewer.id,
            criteriaId
          })
        })
      );

      await Promise.all(ratingPromises);
      
      if (socket && socket.connected) {
        socket.emit('rating-update', {
          candidateId: ratingCandidate.id,
          candidateName: ratingCandidate.name,
          interviewerName: currentInterviewer.name,
          score: Object.values(ratings).reduce((sum, r) => sum + r.score, 0) / Object.values(ratings).length
        });
      }

      setIsRatingOpen(false);
      setRatingCandidate(null);
      fetchData();
    } catch (error) {
      console.error("Error submitting ratings:", error);
    }
  };

  const handleAddCandidate = async () => {
    try {
      const response = await fetch("/api/candidates", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newCandidate)
      });

      if (response.ok) {
        const candidate = await response.json();
        setCandidates([...candidates, candidate]);
        setNewCandidate({ name: "", email: "", phone: "" });
        setIsAddCandidateOpen(false);
      }
    } catch (error) {
      console.error("Error adding candidate:", error);
    }
  };

  const handleDeleteCandidate = async (candidateId: string, candidateName: string) => {
    if (!confirm(`Are you sure you want to delete ${candidateName}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/candidates/${candidateId}`, {
        method: "DELETE"
      });

      if (response.ok) {
        setCandidates(candidates.filter(candidate => candidate.id !== candidateId));
        toast({
          title: "Candidate Deleted",
          description: `${candidateName} has been removed from the system`,
        });
      }
    } catch (error) {
      console.error("Error deleting candidate:", error);
      toast({
        title: "Error",
        description: "Failed to delete candidate",
        variant: "destructive"
      });
    }
  };

  const handleDeleteInterviewer = async (interviewerId: string, interviewerName: string) => {
    if (!confirm(`Are you sure you want to delete ${interviewerName}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/interviewers/${interviewerId}`, {
        method: "DELETE"
      });

      if (response.ok) {
        setInterviewers(interviewers.filter(interviewer => interviewer.id !== interviewerId));
        toast({
          title: "Interviewer Deleted",
          description: `${interviewerName} has been removed from the system`,
        });
      }
    } catch (error) {
      console.error("Error deleting interviewer:", error);
      toast({
        title: "Error",
        description: "Failed to delete interviewer",
        variant: "destructive"
      });
    }
  };

  const handleAddInterviewer = async () => {
    try {
      const response = await fetch("/api/interviewers", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newInterviewer)
      });

      if (response.ok) {
        const interviewer = await response.json();
        setInterviewers([...interviewers, interviewer]);
        setNewInterviewer({ name: "", email: "", role: "" });
        setIsAddInterviewerOpen(false);
      }
    } catch (error) {
      console.error("Error adding interviewer:", error);
    }
  };

  const handleAddCriteria = async () => {
    try {
      const response = await fetch("/api/criteria", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newCriteria)
      });

      if (response.ok) {
        const criteriaItem = await response.json();
        setCriteria([...criteria, criteriaItem]);
        setNewCriteria({ name: "", description: "", weight: 1, maxScore: 10 });
        setIsAddCriteriaOpen(false);
      }
    } catch (error) {
      console.error("Error adding criteria:", error);
    }
  };

  const openCandidateDetail = (candidate: Candidate) => {
    setDetailCandidate(candidate);
    setIsCandidateDetailOpen(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-pink-50 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <header className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-purple-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative w-16 h-16">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                  <Brain className="w-8 h-8 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  AI Interview Selection System
                </h1>
                <p className="text-slate-600 font-medium">
                  Smart CV parsing • AI assessment • Collaborative ranking
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3 bg-purple-100 px-4 py-2 rounded-full">
                <Users className="w-5 h-5 text-purple-600" />
                <span className="text-purple-700 font-semibold">{candidates.length} Candidates</span>
              </div>
              <div className="flex items-center space-x-3 bg-blue-100 px-4 py-2 rounded-full">
                <Star className="w-5 h-5 text-blue-600" />
                <span className="text-blue-700 font-semibold">{interviewers.length} Interviewers</span>
              </div>
              <Dialog open={isBulkUploadOpen} onOpenChange={setIsBulkUploadOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold shadow-lg">
                    <Upload className="w-5 h-5 mr-2" />
                    Bulk CV Upload
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle className="text-2xl font-bold text-purple-800">
                      Bulk CV Upload & AI Processing
                    </DialogTitle>
                    <DialogDescription>
                      Upload multiple CV files for automatic parsing and AI assessment
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      dragActive 
                        ? 'border-purple-400 bg-purple-50' 
                        : 'border-gray-300 hover:border-purple-400'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <Upload className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-700 mb-2">
                      Drag & drop CV files here
                    </p>
                    <p className="text-sm text-gray-500 mb-4">
                      Supports PDF, DOC, DOCX files
                    </p>
                    <input
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx"
                      onChange={handleFileSelect}
                      className="hidden"
                      id="bulk-file-upload"
                    />
                    <label htmlFor="bulk-file-upload">
                      <Button variant="outline" className="cursor-pointer">
                        Choose Files
                      </Button>
                    </label>
                  </div>

                  {cvFiles.length > 0 && (
                    <div className="mt-6 space-y-3">
                      <h3 className="font-semibold text-gray-700">Selected Files:</h3>
                      {cvFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <File className="w-5 h-5 text-purple-500" />
                            <span className="font-medium">{file.name}</span>
                            <Badge variant="secondary">{(file.size / 1024).toFixed(1)} KB</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  <DialogFooter>
                    <Button 
                      onClick={handleBulkUpload}
                      disabled={cvFiles.length === 0 || parsingCV}
                      className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold"
                    >
                      {parsingCV ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Processing...
                        </>
                      ) : (
                        <>
                          <Brain className="w-5 h-5 mr-2" />
                          Process All CVs
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <Tabs defaultValue="candidates" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-white/80 backdrop-blur-sm rounded-xl p-1 shadow-lg">
            <TabsTrigger value="candidates" className="data-[state=active]:bg-purple-500 data-[state=active]:text-white">
              <Users className="w-4 h-4 mr-2" />
              Candidates
            </TabsTrigger>
            <TabsTrigger value="interviewers" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white">
              <Star className="w-4 h-4 mr-2" />
              Interviewers
            </TabsTrigger>
            <TabsTrigger value="criteria" className="data-[state=active]:bg-green-500 data-[state=active]:text-white">
              <Target className="w-4 h-4 mr-2" />
              Criteria
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white">
              <BarChart3 className="w-4 h-4 mr-2" />
              Analytics
            </TabsTrigger>
          </TabsList>

          <TabsContent value="candidates" className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400 w-5 h-5" />
                  <Input
                    placeholder="Search candidates by name or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 w-80 border-purple-200 focus:border-purple-400 rounded-xl"
                  />
                </div>
                <Badge className="bg-gradient-to-r from-purple-400 to-pink-400 text-white">
                  {filteredCandidates.length} Found
                </Badge>
              </div>
              <Dialog open={isAddCandidateOpen} onOpenChange={setIsAddCandidateOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold shadow-lg">
                    <Plus className="w-5 h-5 mr-2" />
                    Add Candidate
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-bold text-purple-800">Add New Candidate</DialogTitle>
                    <DialogDescription>
                      Enter candidate details manually
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name" className="text-purple-700 font-medium">Full Name</Label>
                      <Input
                        id="name"
                        value={newCandidate.name}
                        onChange={(e) => setNewCandidate({ ...newCandidate, name: e.target.value })}
                        className="border-purple-200 focus:border-purple-400"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-purple-700 font-medium">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={newCandidate.email}
                        onChange={(e) => setNewCandidate({ ...newCandidate, email: e.target.value })}
                        className="border-purple-200 focus:border-purple-400"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone" className="text-purple-700 font-medium">Phone (Optional)</Label>
                      <Input
                        id="phone"
                        value={newCandidate.phone}
                        onChange={(e) => setNewCandidate({ ...newCandidate, phone: e.target.value })}
                        className="border-purple-200 focus:border-purple-400"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleAddCandidate} className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold">
                      Add Candidate
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            {/* AI-Powered Candidate Cards */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {sortedCandidates.map((candidate, index) => (
                <Card key={candidate.id} className="bg-white/80 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-300 border border-purple-100 overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-12 h-12 border-2 border-white">
                          <AvatarFallback className="bg-white/20 text-white font-bold">
                            {candidate.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-lg font-bold">{candidate.name}</CardTitle>
                          <CardDescription className="text-purple-100">{candidate.email}</CardDescription>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        {candidate.aiScore && (
                          <Badge className={`${getScoreColor(candidate.aiScore)} text-sm font-bold shadow-lg`}>
                            {candidate.aiScore.toFixed(1)}/10
                          </Badge>
                        )}
                        {index === 0 && candidate.aiScore && candidate.aiScore >= 7 && (
                          <Crown className="w-5 h-5 text-yellow-300" />
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-4 space-y-4">
                    {/* Parsed Information */}
                    {candidate.parsedData && (
                      <div className="bg-purple-50 rounded-lg p-3 space-y-2">
                        <div className="flex items-center space-x-2 text-sm">
                          <UserCheck className="w-4 h-4 text-purple-600" />
                          <span className="font-medium text-purple-800">Parsed Info:</span>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {candidate.parsedData.age && (
                            <div className="bg-white rounded px-2 py-1">
                              <span className="text-gray-600">Age:</span>
                              <span className="font-medium ml-1">{candidate.parsedData.age}</span>
                            </div>
                          )}
                          {candidate.parsedData.experience && (
                            <div className="bg-white rounded px-2 py-1">
                              <span className="text-gray-600">Exp:</span>
                              <span className="font-medium ml-1">{candidate.parsedData.experience}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* AI Score Progress */}
                    {candidate.aiScore && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            <Brain className="w-4 h-4 text-purple-600" />
                            <span className="font-medium text-purple-700">AI Assessment</span>
                          </div>
                          <span className="font-bold text-purple-700">{candidate.aiScore.toFixed(1)}/10</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-purple-400 to-pink-400 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${candidate.aiScore * 10}%` }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {/* Candidate Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4 text-purple-500" />
                        <span>{candidate.cvText ? 'CV Available' : 'No CV'}</span>
                      </div>
                      <span>{new Date(candidate.createdAt).toLocaleDateString()}</span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-green-200 text-green-700 hover:bg-green-50"
                        onClick={() => openCandidateDetail(candidate)}
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-purple-200 text-purple-700 hover:bg-purple-50"
                        onClick={() => openUploadDialog(candidate)}
                      >
                        <Upload className="w-4 h-4 mr-1" />
                        CV
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-blue-200 text-blue-700 hover:bg-blue-50"
                        onClick={() => openRatingDialog(candidate)}
                      >
                        <Star className="w-4 h-4 mr-1" />
                        Rate
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-200 text-red-700 hover:bg-red-50"
                        onClick={() => handleDeleteCandidate(candidate.id, candidate.name)}
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="interviewers" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Interview Team
              </h2>
              <Dialog open={isAddInterviewerOpen} onOpenChange={setIsAddInterviewerOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-bold shadow-lg">
                    <Plus className="w-5 h-5 mr-2" />
                    Add Interviewer
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-bold text-blue-800">Add New Interviewer</DialogTitle>
                    <DialogDescription>
                      Add a new interviewer to the evaluation team
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name" className="text-blue-700 font-medium">Full Name</Label>
                      <Input
                        id="name"
                        value={newInterviewer.name}
                        onChange={(e) => setNewInterviewer({ ...newInterviewer, name: e.target.value })}
                        className="border-blue-200 focus:border-blue-400"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-blue-700 font-medium">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={newInterviewer.email}
                        onChange={(e) => setNewInterviewer({ ...newInterviewer, email: e.target.value })}
                        className="border-blue-200 focus:border-blue-400"
                      />
                    </div>
                    <div>
                      <Label htmlFor="role" className="text-blue-700 font-medium">Role</Label>
                      <Input
                        id="role"
                        value={newInterviewer.role}
                        onChange={(e) => setNewInterviewer({ ...newInterviewer, role: e.target.value })}
                        className="border-blue-200 focus:border-blue-400"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleAddInterviewer} className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-bold">
                      Add Interviewer
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {interviewers.map((interviewer) => (
                <Card key={interviewer.id} className="bg-white/80 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-300 border border-blue-100">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-16 h-16 border-2 border-blue-200">
                        <AvatarFallback className="bg-gradient-to-r from-blue-400 to-purple-400 text-white font-bold text-lg">
                          {interviewer.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          {interviewer.name}
                        </CardTitle>
                        <CardDescription className="text-blue-600 font-medium">{interviewer.email}</CardDescription>
                        {interviewer.role && (
                          <Badge className="mt-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700">
                            {interviewer.role}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        Joined {new Date(interviewer.createdAt).toLocaleDateString()}
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => joinAsInterviewer(interviewer)}
                          className="border-blue-200 text-blue-700 hover:bg-blue-50"
                        >
                          Join Session
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="border-red-200 text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteInterviewer(interviewer.id, interviewer.name)}
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                          Delete
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="criteria" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                Evaluation Criteria
              </h2>
              <Dialog open={isAddCriteriaOpen} onOpenChange={setIsAddCriteriaOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold shadow-lg">
                    <Plus className="w-5 h-5 mr-2" />
                    Add Criteria
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-bold text-green-800">Add New Criteria</DialogTitle>
                    <DialogDescription>
                      Define a new evaluation criterion
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name" className="text-green-700 font-medium">Criteria Name</Label>
                      <Input
                        id="name"
                        value={newCriteria.name}
                        onChange={(e) => setNewCriteria({ ...newCriteria, name: e.target.value })}
                        className="border-green-200 focus:border-green-400"
                      />
                    </div>
                    <div>
                      <Label htmlFor="description" className="text-green-700 font-medium">Description</Label>
                      <Textarea
                        id="description"
                        value={newCriteria.description}
                        onChange={(e) => setNewCriteria({ ...newCriteria, description: e.target.value })}
                        className="border-green-200 focus:border-green-400"
                        rows={3}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="weight" className="text-green-700 font-medium">Weight</Label>
                        <Input
                          id="weight"
                          type="number"
                          value={newCriteria.weight}
                          onChange={(e) => setNewCriteria({ ...newCriteria, weight: parseFloat(e.target.value) })}
                          className="border-green-200 focus:border-green-400"
                        />
                      </div>
                      <div>
                        <Label htmlFor="maxScore" className="text-green-700 font-medium">Max Score</Label>
                        <Input
                          id="maxScore"
                          type="number"
                          value={newCriteria.maxScore}
                          onChange={(e) => setNewCriteria({ ...newCriteria, maxScore: parseInt(e.target.value) })}
                          className="border-green-200 focus:border-green-400"
                        />
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleAddCriteria} className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold">
                      Add Criteria
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {criteria.map((criterion) => (
                <Card key={criterion.id} className="bg-white/80 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-300 border border-green-100">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                        {criterion.name}
                      </CardTitle>
                      <Badge className="bg-gradient-to-r from-green-100 to-blue-100 text-green-700 font-bold">
                        Weight: {criterion.weight}
                      </Badge>
                    </div>
                    {criterion.description && (
                      <CardDescription className="text-green-600">{criterion.description}</CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Max Score: {criterion.maxScore}</span>
                      <span>{new Date(criterion.createdAt).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <AnalyticsDashboard />
          </TabsContent>
        </Tabs>
      </div>

      {/* Rating Dialog */}
      <Dialog open={isRatingOpen} onOpenChange={setIsRatingOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Rate Candidate: {ratingCandidate?.name}
            </DialogTitle>
            <DialogDescription>
              Evaluate this candidate based on the established criteria
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Interviewer Selection */}
            <div className="space-y-2">
              <Label className="text-blue-700 font-medium">Select Interviewer</Label>
              <Select 
                value={currentInterviewer?.id || ''} 
                onValueChange={(value) => {
                  const interviewer = interviewers.find(i => i.id === value);
                  setCurrentInterviewer(interviewer || null);
                }}
              >
                <SelectTrigger className="border-blue-200 focus:border-blue-400">
                  <SelectValue placeholder="Choose interviewer" />
                </SelectTrigger>
                <SelectContent>
                  {interviewers.map((interviewer) => (
                    <SelectItem key={interviewer.id} value={interviewer.id}>
                      {interviewer.name} {interviewer.role && `(${interviewer.role})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Rating Criteria */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800">Evaluation Criteria</h3>
              {criteria.map((criterion) => (
                <Card key={criterion.id} className="border-blue-100">
                  <CardContent className="p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-800">{criterion.name}</h4>
                        {criterion.description && (
                          <p className="text-sm text-gray-600">{criterion.description}</p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-blue-100 text-blue-700">
                          Weight: {criterion.weight}
                        </Badge>
                        <Badge className="bg-purple-100 text-purple-700">
                          Max: {criterion.maxScore}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium text-gray-700">Score</Label>
                        <span className="text-sm font-bold text-blue-600">
                          {ratings[criterion.id]?.score || 0}/{criterion.maxScore}
                        </span>
                      </div>
                      <input
                        type="range"
                        min="0"
                        max={criterion.maxScore}
                        value={ratings[criterion.id]?.score || criterion.maxScore / 2}
                        onChange={(e) => handleRatingChange(criterion.id, parseInt(e.target.value), ratings[criterion.id]?.notes || '')}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-600"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>0</span>
                        <span>{criterion.maxScore / 2}</span>
                        <span>{criterion.maxScore}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Notes</Label>
                      <Textarea
                        placeholder="Add your comments and observations..."
                        value={ratings[criterion.id]?.notes || ''}
                        onChange={(e) => handleRatingChange(criterion.id, ratings[criterion.id]?.score || 0, e.target.value)}
                        rows={2}
                        className="border-gray-200 focus:border-blue-400"
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Rating Summary */}
            <Card className="border-green-100">
              <CardContent className="p-4">
                <h4 className="font-medium text-gray-800 mb-2">Rating Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Average Score:</span>
                    <span className="font-bold ml-2 text-green-600">
                      {Object.values(ratings).length > 0 
                        ? (Object.values(ratings).reduce((sum, r) => sum + r.score, 0) / Object.values(ratings).length).toFixed(1)
                        : '0.0'
                      }/10
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Criteria Rated:</span>
                    <span className="font-bold ml-2 text-blue-600">
                      {Object.keys(ratings).length}/{criteria.length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsRatingOpen(false)}
              className="border-gray-200 text-gray-700"
            >
              Cancel
            </Button>
            <Button 
              onClick={submitRatings}
              disabled={!currentInterviewer || Object.keys(ratings).length === 0}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-bold"
            >
              Submit Rating
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* CV Upload Dialog */}
      <Dialog open={isUploadCVOpen} onOpenChange={setIsUploadCVOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-purple-800">Upload CV</DialogTitle>
            <DialogDescription>
              Upload a CV for {selectedCandidate?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="cv-file" className="text-purple-700 font-medium">Select CV File</Label>
              <input
                id="cv-file"
                type="file"
                accept=".pdf,.doc,.docx"
                onChange={handleUploadCV}
                className="w-full mt-1 border border-purple-200 rounded-md p-2"
                disabled={uploadingCV}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsUploadCVOpen(false)}
              disabled={uploadingCV}
            >
              Cancel
            </Button>
            <Button 
              disabled={uploadingCV}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold"
            >
              {uploadingCV ? 'Uploading...' : 'Upload CV'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Candidate Detail Modal */}
      <CandidateDetailModal
        candidate={detailCandidate}
        isOpen={isCandidateDetailOpen}
        onClose={() => setIsCandidateDetailOpen(false)}
      />
    </div>
  );
}