import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // First, delete all ratings by this interviewer
    await db.rating.deleteMany({
      where: { interviewerId: id }
    })

    // Then delete the interviewer
    const deletedInterviewer = await db.interviewer.delete({
      where: { id }
    })

    return NextResponse.json({ 
      message: 'Interviewer deleted successfully',
      interviewer: deletedInterviewer
    })
  } catch (error) {
    console.error('Error deleting interviewer:', error)
    return NextResponse.json({ error: 'Failed to delete interviewer' }, { status: 500 })
  }
}