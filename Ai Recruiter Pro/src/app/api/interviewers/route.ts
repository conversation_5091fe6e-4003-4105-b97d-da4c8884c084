import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function GET() {
  try {
    const interviewers = await db.interviewer.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(interviewers)
  } catch (error) {
    console.error('Error fetching interviewers:', error)
    return NextResponse.json({ error: 'Failed to fetch interviewers' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, email, role } = await request.json()

    const interviewer = await db.interviewer.create({
      data: {
        name,
        email,
        role
      }
    })

    return NextResponse.json(interviewer)
  } catch (error) {
    console.error('Error creating interviewer:', error)
    return NextResponse.json({ error: 'Failed to create interviewer' }, { status: 500 })
  }
}