import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function GET() {
  try {
    // Get all candidates with their ratings
    const candidates = await db.candidate.findMany({
      include: {
        ratings: {
          include: {
            interviewer: true,
            criteria: true
          }
        }
      }
    })

    // Get all criteria
    const criteria = await db.criteria.findMany()

    // Get all interviewers
    const interviewers = await db.interviewer.findMany({
      include: {
        ratings: true
      }
    })

    // Calculate analytics
    const totalCandidates = candidates.length
    const candidatesWithCV = candidates.filter(c => c.cvText).length
    const candidatesWithAIScore = candidates.filter(c => c.aiScore).length
    const candidatesWithRatings = candidates.filter(c => c.ratings.length > 0).length

    // Score distribution
    const scoreDistribution = {
      '0-2': candidates.filter(c => (c.aiScore || 0) >= 0 && (c.aiScore || 0) < 2).length,
      '2-4': candidates.filter(c => (c.aiScore || 0) >= 2 && (c.aiScore || 0) < 4).length,
      '4-6': candidates.filter(c => (c.aiScore || 0) >= 4 && (c.aiScore || 0) < 6).length,
      '6-8': candidates.filter(c => (c.aiScore || 0) >= 6 && (c.aiScore || 0) < 8).length,
      '8-10': candidates.filter(c => (c.aiScore || 0) >= 8 && (c.aiScore || 0) <= 10).length
    }

    // Top candidates
    const topCandidates = candidates
      .filter(c => c.aiScore)
      .sort((a, b) => (b.aiScore || 0) - (a.aiScore || 0))
      .slice(0, 10)
      .map(c => ({
        id: c.id,
        name: c.name,
        email: c.email,
        aiScore: c.aiScore,
        ratingsCount: c.ratings.length
      }))

    // Criteria performance
    const criteriaPerformance = criteria.map(criterion => {
      const ratings = candidates.flatMap(c => 
        c.ratings.filter(r => r.criteriaId === criterion.id)
      )
      
      const avgScore = ratings.length > 0 
        ? ratings.reduce((sum, r) => sum + r.score, 0) / ratings.length 
        : 0

      return {
        id: criterion.id,
        name: criterion.name,
        averageScore: avgScore,
        ratingsCount: ratings.length,
        weight: criterion.weight,
        maxScore: criterion.maxScore
      }
    })

    // Interviewer activity
    const interviewerActivity = interviewers.map(interviewer => ({
      id: interviewer.id,
      name: interviewer.name,
      email: interviewer.email,
      role: interviewer.role,
      ratingsGiven: interviewer.ratings.length,
      candidatesEvaluated: new Set(interviewer.ratings.map(r => r.candidateId)).size
    }))

    // Time-based analytics (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentCandidates = candidates.filter(c => 
      new Date(c.createdAt) >= thirtyDaysAgo
    )

    const recentActivity = {
      newCandidates: recentCandidates.length,
      cvsParsed: recentCandidates.filter(c => c.cvText).length,
      aiAnalysesCompleted: recentCandidates.filter(c => c.aiScore).length
    }

    // Skills analysis
    const allSkills = candidates
      .map(c => {
        try {
          const parsed = c.parsedData ? JSON.parse(c.parsedData) : null
          return parsed?.skills || []
        } catch {
          return []
        }
      })
      .flat()
      .filter(skill => skill && typeof skill === 'string')

    const skillsFrequency = allSkills.reduce((acc, skill) => {
      const normalizedSkill = skill.toLowerCase()
      acc[normalizedSkill] = (acc[normalizedSkill] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const topSkills = Object.entries(skillsFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([skill, count]) => ({ skill, count }))

    // Experience distribution
    const experienceDistribution = candidates.reduce((acc, candidate) => {
      const exp = candidate.experience || 'Not specified'
      acc[exp] = (acc[exp] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Average scores by criteria
    const avgScoresByCriteria = criteria.map(criterion => {
      const allRatings = candidates.flatMap(c => 
        c.ratings.filter(r => r.criteriaId === criterion.id)
      )
      
      const avgScore = allRatings.length > 0 
        ? allRatings.reduce((sum, r) => sum + r.score, 0) / allRatings.length 
        : 0

      return {
        criteriaName: criterion.name,
        averageScore: Math.round(avgScore * 100) / 100,
        totalRatings: allRatings.length
      }
    })

    // Conversion funnel
    const conversionFunnel = {
      totalCandidates,
      withCV: candidatesWithCV,
      withAIScore: candidatesWithAIScore,
      withRatings: candidatesWithRatings,
      conversionRates: {
        cvUpload: totalCandidates > 0 ? (candidatesWithCV / totalCandidates) * 100 : 0,
        aiAnalysis: candidatesWithCV > 0 ? (candidatesWithAIScore / candidatesWithCV) * 100 : 0,
        humanReview: candidatesWithAIScore > 0 ? (candidatesWithRatings / candidatesWithAIScore) * 100 : 0
      }
    }

    return NextResponse.json({
      summary: {
        totalCandidates,
        candidatesWithCV,
        candidatesWithAIScore,
        candidatesWithRatings,
        totalInterviewers: interviewers.length,
        totalCriteria: criteria.length
      },
      scoreDistribution,
      topCandidates,
      criteriaPerformance,
      interviewerActivity,
      recentActivity,
      topSkills,
      experienceDistribution,
      avgScoresByCriteria,
      conversionFunnel
    })
  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 })
  }
}
