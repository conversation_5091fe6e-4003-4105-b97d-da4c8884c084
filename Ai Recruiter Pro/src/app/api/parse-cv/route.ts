import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { db } from '@/lib/db'
import { CVParser } from '@/lib/cv-parser'
import { validateCVFile, createErrorResponse, createSuccessResponse } from '@/lib/validation'
import { handleApiError, Logger, rateLimiter } from '@/lib/error-handler'
import ZAI from 'z-ai-web-dev-sdk'

export async function POST(request: NextRequest) {
  try {
    Logger.info('CV parsing request received');

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    if (!rateLimiter.isAllowed(clientIP)) {
      return NextResponse.json(createErrorResponse('Too many requests. Please try again later.'), { status: 429 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(createErrorResponse('File is required'), { status: 400 });
    }

    // Validate file
    const fileValidation = validateCVFile(file);
    if (!fileValidation.isValid) {
      return NextResponse.json(createErrorResponse('Invalid file', fileValidation.errors), { status: 400 });
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Ensure uploads directory exists
    const uploadsDir = join(process.cwd(), 'public', 'uploads')
    try {
      await mkdir(uploadsDir, { recursive: true })
    } catch (error) {
      // Directory already exists or other error, continue
    }

    // Generate unique filename (sanitize file name)
    const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
    const fileName = `${Date.now()}-${sanitizedName}`
    const path = join(uploadsDir, fileName)
    
    // Save file
    await writeFile(path, buffer)

    // Extract text from file using proper CV parser
    let cvText = ''
    let regexParsedData = null

    try {
      const parseResult = await CVParser.parseCV(buffer, file.type)
      cvText = parseResult.text
      regexParsedData = parseResult.parsed
    } catch (parseError) {
      console.error('CV parsing error:', parseError)
      // Fallback to basic file info
      cvText = `CV File: ${file.name} - Size: ${file.size} bytes - Type: ${file.type} - Parse Error: ${parseError.message}`
      regexParsedData = CVParser.generateFallbackData(file.name)
    }

    // Initialize ZAI SDK for CV parsing
    const zai = await ZAI.create()

    // Create prompt for CV parsing
    const parsePrompt = `
    You are an expert CV parser. Extract the following information from the CV text below and return it in JSON format:
    
    1. Full name
    2. Email address
    3. Phone number
    4. Age (if mentioned)
    5. Years of experience (estimate if not explicitly stated)
    6. Key skills (array of technical and soft skills)
    7. Education level
    8. Current role/title (if mentioned)
    
    CV Text:
    ${cvText}
    
    Please respond in JSON format with the following structure:
    {
      "name": "string",
      "email": "string",
      "phone": "string",
      "age": number,
      "experience": "string",
      "skills": ["string"],
      "education": "string",
      "currentRole": "string"
    }
    
    If any information is not found, use null or empty string as appropriate.
    `

    // Parse CV with AI
    const parseCompletion = await zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert CV parser specializing in extracting structured information from resumes and CVs.'
        },
        {
          role: 'user',
          content: parsePrompt
        }
      ],
      temperature: 0.1
    })

    let aiParsedData
    try {
      aiParsedData = JSON.parse(parseCompletion.choices[0]?.message?.content || '{}')
    } catch (parseError) {
      console.error('AI JSON parsing error:', parseError)
      // Use regex parsed data as fallback
      aiParsedData = regexParsedData || {
        name: null,
        email: null,
        phone: null,
        age: null,
        experience: null,
        skills: [],
        education: null,
        currentRole: null
      }
    }

    // Merge regex and AI parsed data, preferring AI data when available
    const parsedData = {
      name: aiParsedData.name || regexParsedData?.name || null,
      email: aiParsedData.email || regexParsedData?.email || null,
      phone: aiParsedData.phone || regexParsedData?.phone || null,
      age: aiParsedData.age || regexParsedData?.age || null,
      experience: aiParsedData.experience || regexParsedData?.experience || null,
      skills: aiParsedData.skills || regexParsedData?.skills || [],
      education: aiParsedData.education || regexParsedData?.education || null,
      currentRole: aiParsedData.currentRole || regexParsedData?.currentRole || null,
      summary: aiParsedData.summary || regexParsedData?.summary || null,
      location: aiParsedData.location || regexParsedData?.location || null,
      linkedin: aiParsedData.linkedin || regexParsedData?.linkedin || null,
      github: aiParsedData.github || regexParsedData?.github || null
    }

    // For demo purposes, enhance the CV text with more realistic content
    const enhancedCVText = `
    ${cvText}
    
    AI PARSED INFORMATION:
    Name: ${parsedData.name || 'Not found'}
    Email: ${parsedData.email || 'Not found'}
    Phone: ${parsedData.phone || 'Not found'}
    Age: ${parsedData.age || 'Not specified'}
    Experience: ${parsedData.experience || 'Not specified'}
    Skills: ${parsedData.skills?.join(', ') || 'Not specified'}
    Education: ${parsedData.education || 'Not specified'}
    Current Role: ${parsedData.currentRole || 'Not specified'}
    `

    return NextResponse.json({ 
      cvUrl: `/uploads/${fileName}`,
      cvText: enhancedCVText,
      parsedData
    })
  } catch (error) {
    console.error('Error parsing CV:', error)
    return NextResponse.json({ error: 'Failed to parse CV', details: error.message }, { status: 500 })
  }
}