import { NextResponse } from "next/server";
import { db } from '@/lib/db';
import { monitoring } from '@/lib/monitoring';
import { logger } from '@/lib/logger';

export async function GET() {
  const start = Date.now();

  try {
    logger.info('Health check requested');

    // Perform comprehensive health check
    const healthCheck = await monitoring.performHealthCheck();

    // Get additional stats
    const candidatesCount = await db.candidate.count();
    const interviewersCount = await db.interviewer.count();
    const criteriaCount = await db.criteria.count();

    // Get performance metrics
    const performanceMetrics = monitoring.collectPerformanceMetrics();

    // Check for alerts
    const alerts = monitoring.checkAlerts();

    const response = {
      ...healthCheck,
      stats: {
        candidates: candidatesCount,
        interviewers: interviewersCount,
        criteria: criteriaCount,
      },
      performance: {
        memory: performanceMetrics.memory,
        requests: performanceMetrics.requests,
      },
      alerts: alerts.length > 0 ? alerts : undefined,
    };

    // Record the health check request
    const duration = Date.now() - start;
    monitoring.recordRequest('/api/health', 'GET', 200, duration);

    logger.info(`Health check completed in ${duration}ms`, { status: healthCheck.status });

    // Return appropriate status code based on health
    const statusCode = healthCheck.status === 'healthy' ? 200 :
                      healthCheck.status === 'degraded' ? 200 : 503;

    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    const duration = Date.now() - start;
    logger.error("Health check failed", error);
    monitoring.recordRequest('/api/health', 'GET', 503, duration);

    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: "Health check failed",
        checks: {
          database: { status: 'fail', message: 'Health check error' },
          memory: { status: 'fail', message: 'Health check error' },
          disk: { status: 'fail', message: 'Health check error' },
          api: { status: 'fail', message: 'Health check error' },
        },
        version: process.env.npm_package_version || '0.1.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: 0,
      },
      { status: 503 }
    );
  }
}