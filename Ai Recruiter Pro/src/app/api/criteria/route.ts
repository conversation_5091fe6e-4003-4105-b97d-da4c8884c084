import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function GET() {
  try {
    const criteria = await db.criteria.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(criteria)
  } catch (error) {
    console.error('Error fetching criteria:', error)
    return NextResponse.json({ error: 'Failed to fetch criteria' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, description, weight, maxScore } = await request.json()

    const criteriaItem = await db.criteria.create({
      data: {
        name,
        description,
        weight: weight || 1.0,
        maxScore: maxScore || 10
      }
    })

    return NextResponse.json(criteriaItem)
  } catch (error) {
    console.error('Error creating criteria:', error)
    return NextResponse.json({ error: 'Failed to create criteria' }, { status: 500 })
  }
}