import { NextRequest, NextResponse } from 'next/server'
import { writeFile } from 'fs/promises'
import { join } from 'path'
import { db } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const candidateId = formData.get('candidateId') as string

    if (!file || !candidateId) {
      return NextResponse.json({ error: 'Missing file or candidate ID' }, { status: 400 })
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Generate unique filename
    const fileName = `${candidateId}-${Date.now()}-${file.name}`
    const path = join(process.cwd(), 'public', 'uploads', fileName)
    
    // Save file
    await writeFile(path, buffer)

    // Extract text from file (simple implementation - in production, use proper OCR/PDF parser)
    const cvText = `CV for ${candidateId} - File: ${file.name} - Size: ${file.size} bytes`

    // Update candidate record
    const candidate = await db.candidate.update({
      where: { id: candidateId },
      data: {
        cvUrl: `/uploads/${fileName}`,
        cvText
      }
    })

    return NextResponse.json({ 
      cvUrl: `/uploads/${fileName}`,
      cvText,
      candidate
    })
  } catch (error) {
    console.error('Error uploading file:', error)
    return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 })
  }
}