import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import ZA<PERSON> from 'z-ai-web-dev-sdk';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { candidateId } = body;

    if (!candidateId) {
      return NextResponse.json({ error: 'Candidate ID is required' }, { status: 400 });
    }

    // Get candidate data
    const candidate = await db.candidate.findUnique({
      where: { id: candidateId }
    });

    if (!candidate) {
      return NextResponse.json({ error: 'Candidate not found' }, { status: 404 });
    }

    if (!candidate.cvText) {
      return NextResponse.json({ error: 'No CV text available for analysis' }, { status: 400 });
    }

    // Initialize ZAI SDK
    const zai = await ZAI.create();

    // Analyze CV using AI
    const analysisPrompt = `
    You are an expert recruiter analyzing a candidate's CV for a technical position. 
    Please analyze the following CV text and provide:

    1. An overall score from 0-10 based on the candidate's qualifications
    2. A detailed analysis of their strengths and weaknesses
    3. Key skills and experience highlighted
    4. Recommendations for the hiring team

    CV Text:
    ${candidate.cvText}

    Please provide your response in the following JSON format:
    {
      "score": <number between 0-10>,
      "analysis": "<detailed text analysis>",
      "strengths": ["<strength 1>", "<strength 2>", ...],
      "weaknesses": ["<weakness 1>", "<weakness 2>", ...],
      "keySkills": ["<skill 1>", "<skill 2>", ...],
      "recommendations": "<text recommendations>"
    }
    `;

    const completion = await zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert recruiter and technical interviewer. Provide detailed, objective analysis of candidate CVs.'
        },
        {
          role: 'user',
          content: analysisPrompt
        }
      ],
      temperature: 0.3,
      max_tokens: 1000
    });

    const analysisContent = completion.choices[0]?.message?.content;
    
    if (!analysisContent) {
      throw new Error('No analysis received from AI');
    }

    // Parse the JSON response
    let analysisResult;
    try {
      analysisResult = JSON.parse(analysisContent);
    } catch (parseError) {
      // If JSON parsing fails, create a basic analysis structure
      analysisResult = {
        score: 5.0,
        analysis: analysisContent,
        strengths: [],
        weaknesses: [],
        keySkills: [],
        recommendations: 'Please review the CV manually for detailed assessment.'
      };
    }

    // Update candidate with AI analysis
    await db.candidate.update({
      where: { id: candidateId },
      data: {
        aiScore: analysisResult.score,
        aiAnalysis: JSON.stringify(analysisResult)
      }
    });

    return NextResponse.json({
      message: 'AI analysis completed successfully',
      score: analysisResult.score,
      analysis: analysisResult
    });

  } catch (error) {
    console.error('Error in AI analysis:', error);
    return NextResponse.json({ 
      error: 'Failed to analyze CV with AI',
      details: error.message 
    }, { status: 500 });
  }
}