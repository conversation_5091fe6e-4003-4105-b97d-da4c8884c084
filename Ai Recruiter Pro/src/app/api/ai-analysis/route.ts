import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import ZAI from 'z-ai-web-dev-sdk'

export async function POST(request: NextRequest) {
  try {
    const { candidateId, cvText } = await request.json()

    if (!candidateId || !cvText) {
      return NextResponse.json({ error: 'Missing candidate ID or CV text' }, { status: 400 })
    }

    // Initialize ZAI SDK
    const zai = await ZAI.create()

    // Create comprehensive prompt for AI analysis
    const prompt = `
    You are an expert technical recruiter and hiring manager. Analyze the following CV text and provide a comprehensive assessment.
    
    CV Text:
    ${cvText}
    
    Please evaluate the candidate based on these criteria:
    1. Technical Skills & Expertise (0-10 points)
    2. Experience Level & Relevance (0-10 points) 
    3. Education & Certifications (0-10 points)
    4. Project Experience & Achievements (0-10 points)
    5. Communication & Presentation (0-10 points)
    6. Problem Solving & Analytical Skills (0-10 points)
    7. Team Collaboration & Leadership (0-10 points)
    8. Cultural Fit & Professionalism (0-10 points)
    
    For each criterion, provide:
    - A score from 1-10
    - Specific justification based on the CV content
    - Areas of strength
    - Areas for improvement
    
    Then provide:
    - An overall score (weighted average of all criteria)
    - A hire/no-hire recommendation
    - Suggested interview questions
    - Career level assessment (Junior, Mid-level, Senior, Lead)
    - Salary range recommendation
    - Key strengths summary
    - Potential concerns or red flags
    
    Please respond in JSON format with the following structure:
    {
      "overallScore": number,
      "criteria": [
        {
          "name": "Technical Skills & Expertise",
          "score": number,
          "justification": "string",
          "strengths": ["string"],
          "improvements": ["string"]
        },
        ... other criteria ...
      ],
      "recommendation": "HIRE" | "CONSIDER" | "PASS",
      "careerLevel": "Junior" | "Mid-level" | "Senior" | "Lead",
      "salaryRange": "string",
      "keyStrengths": ["string"],
      "concerns": ["string"],
      "interviewQuestions": ["string"],
      "summary": "string"
    }
    
    Be objective, thorough, and base your assessment solely on the information provided in the CV.
    `

    // Get AI analysis
    const completion = await zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert recruiter specializing in technical talent assessment.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3
    })

    let aiAnalysis
    try {
      aiAnalysis = JSON.parse(completion.choices[0]?.message?.content || '{}')
    } catch (parseError) {
      // Fallback if JSON parsing fails
      aiAnalysis = {
        overallScore: 5,
        criteria: [
          {
            name: "Technical Skills & Expertise",
            score: 5,
            justification: "Unable to analyze due to parsing error",
            strengths: ["Unable to determine"],
            improvements: ["Manual review required"]
          }
        ],
        recommendation: "CONSIDER",
        careerLevel: "Mid-level",
        salaryRange: "Market rate",
        keyStrengths: ["Unable to parse AI response"],
        concerns: ["AI analysis failed"],
        interviewQuestions: ["General technical questions"],
        summary: completion.choices[0]?.message?.content || 'AI analysis unavailable'
      }
    }

    // Update candidate with AI analysis
    const candidate = await db.candidate.update({
      where: { id: candidateId },
      data: {
        aiScore: aiAnalysis.overallScore,
        aiAnalysis: JSON.stringify(aiAnalysis)
      }
    })

    return NextResponse.json({ 
      aiScore: aiAnalysis.overallScore,
      aiAnalysis,
      candidate
    })
  } catch (error) {
    console.error('Error performing AI analysis:', error)
    return NextResponse.json({ error: 'Failed to perform AI analysis' }, { status: 500 })
  }
}