import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // First, delete all ratings for this candidate
    await db.rating.deleteMany({
      where: { candidateId: id }
    })

    // Then delete the candidate
    const deletedCandidate = await db.candidate.delete({
      where: { id }
    })

    return NextResponse.json({ 
      message: 'Candidate deleted successfully',
      candidate: deletedCandidate
    })
  } catch (error) {
    console.error('Error deleting candidate:', error)
    return NextResponse.json({ error: 'Failed to delete candidate' }, { status: 500 })
  }
}