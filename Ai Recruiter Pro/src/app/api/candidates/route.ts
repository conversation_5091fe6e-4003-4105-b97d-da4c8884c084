import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { candidateCreateSchema, validateRequest, validateQueryParams, paginationSchema, candidateFilterSchema, createErrorResponse, createSuccessResponse } from '@/lib/validation'
import { handleApiError, Logger } from '@/lib/error-handler'

export async function GET(request: NextRequest) {
  try {
    Logger.info('Fetching candidates');

    const { searchParams } = new URL(request.url);

    // Validate query parameters
    const paginationValidation = validateQueryParams(paginationSchema, searchParams);
    if (!paginationValidation.success) {
      return NextResponse.json(createErrorResponse('Invalid pagination parameters', paginationValidation.errors), { status: 400 });
    }

    const filterValidation = validateQueryParams(candidateFilterSchema, searchParams);
    if (!filterValidation.success) {
      return NextResponse.json(createErrorResponse('Invalid filter parameters', filterValidation.errors), { status: 400 });
    }

    const { page, limit, sortBy, sortOrder } = paginationValidation.data;
    const { search, hasCV, hasAIScore, minScore, maxScore, experience } = filterValidation.data;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (hasCV !== undefined) {
      where.cvText = hasCV ? { not: null } : null;
    }

    if (hasAIScore !== undefined) {
      where.aiScore = hasAIScore ? { not: null } : null;
    }

    if (minScore !== undefined) {
      where.aiScore = { ...where.aiScore, gte: minScore };
    }

    if (maxScore !== undefined) {
      where.aiScore = { ...where.aiScore, lte: maxScore };
    }

    if (experience) {
      where.experience = { contains: experience, mode: 'insensitive' };
    }

    // Build order by
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder;
    } else {
      orderBy.createdAt = 'desc';
    }

    // Get total count for pagination
    const totalCount = await db.candidate.count({ where });

    // Get candidates with pagination
    const candidates = await db.candidate.findMany({
      where,
      include: {
        ratings: {
          include: {
            interviewer: true,
            criteria: true
          }
        }
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit
    });

    // Transform parsedData from JSON string to object
    const transformedCandidates = candidates.map(candidate => ({
      ...candidate,
      parsedData: candidate.parsedData ? JSON.parse(candidate.parsedData) : null
    }));

    const response = createSuccessResponse({
      candidates: transformedCandidates,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      }
    });

    Logger.info(`Fetched ${candidates.length} candidates (page ${page})`);
    return NextResponse.json(response);
  } catch (error) {
    Logger.error('Error fetching candidates:', error);
    return handleApiError(error);
  }
}

export async function POST(request: NextRequest) {
  try {
    Logger.info('Creating new candidate');

    const body = await request.json();

    // Validate request body
    const validation = validateRequest(candidateCreateSchema, body);
    if (!validation.success) {
      return NextResponse.json(createErrorResponse('Validation failed', validation.errors), { status: 400 });
    }

    const { name, email, phone, age, experience, cvText, cvUrl, parsedData } = validation.data;

    // Check if candidate with email already exists
    const existingCandidate = await db.candidate.findUnique({
      where: { email }
    });

    if (existingCandidate) {
      return NextResponse.json(createErrorResponse('Candidate with this email already exists'), { status: 409 });
    }

    const candidate = await db.candidate.create({
      data: {
        name,
        email,
        phone,
        age,
        experience,
        cvText,
        cvUrl,
        parsedData: parsedData ? JSON.stringify(parsedData) : null
      }
    });

    // Transform parsedData for response
    const transformedCandidate = {
      ...candidate,
      parsedData: candidate.parsedData ? JSON.parse(candidate.parsedData) : null
    };

    Logger.info(`Created candidate: ${candidate.name} (${candidate.id})`);
    return NextResponse.json(createSuccessResponse(transformedCandidate, 'Candidate created successfully'), { status: 201 });
  } catch (error) {
    Logger.error('Error creating candidate:', error);
    return handleApiError(error);
  }
}