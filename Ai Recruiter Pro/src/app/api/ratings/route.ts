import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function GET() {
  try {
    const ratings = await db.rating.findMany({
      include: {
        candidate: true,
        interviewer: true,
        criteria: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(ratings)
  } catch (error) {
    console.error('Error fetching ratings:', error)
    return NextResponse.json({ error: 'Failed to fetch ratings' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { score, notes, candidateId, interviewerId, criteriaId } = await request.json()

    // Check if rating already exists
    const existingRating = await db.rating.findUnique({
      where: {
        candidateId_interviewerId_criteriaId: {
          candidateId,
          interviewerId,
          criteriaId
        }
      }
    })

    let rating
    if (existingRating) {
      // Update existing rating
      rating = await db.rating.update({
        where: { id: existingRating.id },
        data: { score, notes }
      })
    } else {
      // Create new rating
      rating = await db.rating.create({
        data: {
          score,
          notes,
          candidateId,
          interviewerId,
          criteriaId
        }
      })
    }

    // Calculate and update candidate's average score
    const allRatings = await db.rating.findMany({
      where: { candidateId },
      include: { criteria: true }
    })

    let totalWeightedScore = 0
    let totalWeight = 0

    allRatings.forEach(rating => {
      const weight = rating.criteria.weight
      totalWeightedScore += rating.score * weight
      totalWeight += weight
    })

    const averageScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0

    await db.candidate.update({
      where: { id: candidateId },
      data: { averageScore }
    })

    return NextResponse.json(rating)
  } catch (error) {
    console.error('Error creating/updating rating:', error)
    return NextResponse.json({ error: 'Failed to create/update rating' }, { status: 500 })
  }
}