"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, PieChart, Pie, Cell, ResponsiveContainer, LineChart, Line, Area, AreaChart } from "recharts";
import { TrendingUp, Users, FileText, Brain, Award, Target, Activity, Clock } from "lucide-react";

interface AnalyticsData {
  summary: {
    totalCandidates: number;
    candidatesWithCV: number;
    candidatesWithAIScore: number;
    candidatesWithRatings: number;
    totalInterviewers: number;
    totalCriteria: number;
  };
  scoreDistribution: Record<string, number>;
  topCandidates: Array<{
    id: string;
    name: string;
    email: string;
    aiScore: number;
    ratingsCount: number;
  }>;
  criteriaPerformance: Array<{
    id: string;
    name: string;
    averageScore: number;
    ratingsCount: number;
    weight: number;
    maxScore: number;
  }>;
  interviewerActivity: Array<{
    id: string;
    name: string;
    email: string;
    role: string;
    ratingsGiven: number;
    candidatesEvaluated: number;
  }>;
  recentActivity: {
    newCandidates: number;
    cvsParsed: number;
    aiAnalysesCompleted: number;
  };
  topSkills: Array<{
    skill: string;
    count: number;
  }>;
  conversionFunnel: {
    totalCandidates: number;
    withCV: number;
    withAIScore: number;
    withRatings: number;
    conversionRates: {
      cvUpload: number;
      aiAnalysis: number;
      humanReview: number;
    };
  };
}

export function AnalyticsDashboard() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      const response = await fetch('/api/analytics');
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Failed to load analytics data</p>
      </div>
    );
  }

  const scoreDistributionData = Object.entries(analytics.scoreDistribution).map(([range, count]) => ({
    range,
    count,
    fill: range === '8-10' ? '#10b981' : range === '6-8' ? '#3b82f6' : range === '4-6' ? '#f59e0b' : '#ef4444'
  }));

  const criteriaChartData = analytics.criteriaPerformance.map(criteria => ({
    name: criteria.name.substring(0, 15) + (criteria.name.length > 15 ? '...' : ''),
    score: criteria.averageScore,
    ratings: criteria.ratingsCount
  }));

  const skillsChartData = analytics.topSkills.slice(0, 10).map(skill => ({
    skill: skill.skill,
    count: skill.count
  }));

  const funnelData = [
    { stage: 'Total Candidates', count: analytics.conversionFunnel.totalCandidates, rate: 100 },
    { stage: 'With CV', count: analytics.conversionFunnel.withCV, rate: analytics.conversionFunnel.conversionRates.cvUpload },
    { stage: 'AI Analyzed', count: analytics.conversionFunnel.withAIScore, rate: analytics.conversionFunnel.conversionRates.aiAnalysis },
    { stage: 'Human Reviewed', count: analytics.conversionFunnel.withRatings, rate: analytics.conversionFunnel.conversionRates.humanReview }
  ];

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Candidates</CardTitle>
            <Users className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.summary.totalCandidates}</div>
            <p className="text-xs text-blue-100">
              {analytics.summary.candidatesWithCV} with CVs
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Analyzed</CardTitle>
            <Brain className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.summary.candidatesWithAIScore}</div>
            <p className="text-xs text-green-100">
              {((analytics.summary.candidatesWithAIScore / analytics.summary.totalCandidates) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Human Reviewed</CardTitle>
            <Award className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.summary.candidatesWithRatings}</div>
            <p className="text-xs text-purple-100">
              By {analytics.summary.totalInterviewers} interviewers
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
            <Activity className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.recentActivity.newCandidates}</div>
            <p className="text-xs text-orange-100">
              New candidates (30 days)
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 1 */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-500" />
              Score Distribution
            </CardTitle>
            <CardDescription>
              Distribution of AI assessment scores
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={{}} className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={scoreDistributionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="range" />
                  <YAxis />
                  <ChartTooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-500" />
              Criteria Performance
            </CardTitle>
            <CardDescription>
              Average scores by evaluation criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={{}} className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={criteriaChartData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" domain={[0, 10]} />
                  <YAxis dataKey="name" type="category" width={100} />
                  <ChartTooltip />
                  <Bar dataKey="score" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-purple-500" />
              Top Skills
            </CardTitle>
            <CardDescription>
              Most common skills across candidates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={{}} className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={skillsChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="skill" angle={-45} textAnchor="end" height={80} />
                  <YAxis />
                  <ChartTooltip />
                  <Bar dataKey="count" fill="#8b5cf6" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-500" />
              Conversion Funnel
            </CardTitle>
            <CardDescription>
              Candidate progression through evaluation stages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {funnelData.map((stage, index) => (
                <div key={stage.stage} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{stage.stage}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">{stage.count}</span>
                      <Badge variant="secondary">{stage.rate.toFixed(1)}%</Badge>
                    </div>
                  </div>
                  <Progress value={stage.rate} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Candidates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5 text-yellow-500" />
            Top Candidates
          </CardTitle>
          <CardDescription>
            Highest scoring candidates by AI assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.topCandidates.slice(0, 5).map((candidate, index) => (
              <div key={candidate.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-yellow-400 to-yellow-500 text-white rounded-full font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{candidate.name}</p>
                    <p className="text-sm text-gray-600">{candidate.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className="bg-gradient-to-r from-green-400 to-green-500 text-white">
                    {candidate.aiScore.toFixed(1)}/10
                  </Badge>
                  <span className="text-sm text-gray-500">{candidate.ratingsCount} ratings</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
