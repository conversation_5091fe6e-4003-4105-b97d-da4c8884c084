"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Briefcase, 
  GraduationCap, 
  Code, 
  Brain, 
  Star, 
  FileText, 
  Download,
  ExternalLink,
  MapPin,
  Github,
  Linkedin
} from "lucide-react";

interface Candidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  age?: number;
  experience?: string;
  cvUrl?: string;
  cvText?: string;
  aiScore?: number;
  aiAnalysis?: string;
  averageScore?: number;
  createdAt: string;
  parsedData?: {
    name?: string;
    email?: string;
    phone?: string;
    age?: number;
    experience?: string;
    skills?: string[];
    education?: string;
    currentRole?: string;
    summary?: string;
    location?: string;
    linkedin?: string;
    github?: string;
  };
  ratings?: Array<{
    id: string;
    score: number;
    notes?: string;
    criteria: {
      name: string;
      maxScore: number;
    };
    interviewer: {
      name: string;
      role?: string;
    };
    createdAt: string;
  }>;
}

interface CandidateDetailModalProps {
  candidate: Candidate | null;
  isOpen: boolean;
  onClose: () => void;
}

export function CandidateDetailModal({ candidate, isOpen, onClose }: CandidateDetailModalProps) {
  const [activeTab, setActiveTab] = useState("overview");

  if (!candidate) return null;

  const aiAnalysis = candidate.aiAnalysis ? JSON.parse(candidate.aiAnalysis) : null;
  const parsedData = candidate.parsedData;

  const getScoreColor = (score: number) => {
    if (score >= 8) return "bg-gradient-to-r from-green-400 to-green-600 text-white";
    if (score >= 6) return "bg-gradient-to-r from-yellow-400 to-yellow-600 text-white";
    return "bg-gradient-to-r from-red-400 to-red-600 text-white";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-4">
            <Avatar className="w-16 h-16 border-2 border-purple-200">
              <AvatarFallback className="bg-gradient-to-r from-purple-400 to-pink-400 text-white font-bold text-lg">
                {candidate.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <DialogTitle className="text-2xl font-bold text-purple-800">
                {candidate.name}
              </DialogTitle>
              <DialogDescription className="text-lg text-purple-600">
                {parsedData?.currentRole || 'Candidate Profile'}
              </DialogDescription>
              <div className="flex items-center gap-2 mt-2">
                {candidate.aiScore && (
                  <Badge className={`${getScoreColor(candidate.aiScore)} font-bold shadow-lg`}>
                    AI Score: {candidate.aiScore.toFixed(1)}/10
                  </Badge>
                )}
                {candidate.averageScore && (
                  <Badge variant="outline" className="border-blue-300 text-blue-700">
                    Avg Rating: {candidate.averageScore.toFixed(1)}/10
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analysis">AI Analysis</TabsTrigger>
            <TabsTrigger value="ratings">Ratings</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-500" />
                    Personal Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{candidate.email}</span>
                  </div>
                  {candidate.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{candidate.phone}</span>
                    </div>
                  )}
                  {parsedData?.age && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{parsedData.age} years old</span>
                    </div>
                  )}
                  {parsedData?.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{parsedData.location}</span>
                    </div>
                  )}
                  {parsedData?.linkedin && (
                    <div className="flex items-center gap-2">
                      <Linkedin className="h-4 w-4 text-blue-600" />
                      <a href={parsedData.linkedin} target="_blank" rel="noopener noreferrer" 
                         className="text-sm text-blue-600 hover:underline flex items-center gap-1">
                        LinkedIn Profile <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  )}
                  {parsedData?.github && (
                    <div className="flex items-center gap-2">
                      <Github className="h-4 w-4 text-gray-800" />
                      <a href={parsedData.github} target="_blank" rel="noopener noreferrer" 
                         className="text-sm text-gray-800 hover:underline flex items-center gap-1">
                        GitHub Profile <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Professional Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Briefcase className="h-5 w-5 text-green-500" />
                    Professional Background
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {candidate.experience && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{candidate.experience} experience</span>
                    </div>
                  )}
                  {parsedData?.education && (
                    <div className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{parsedData.education}</span>
                    </div>
                  )}
                  {parsedData?.currentRole && (
                    <div className="flex items-center gap-2">
                      <Briefcase className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{parsedData.currentRole}</span>
                    </div>
                  )}
                  <div className="text-xs text-gray-500">
                    Applied on {formatDate(candidate.createdAt)}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Skills */}
            {parsedData?.skills && parsedData.skills.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5 text-purple-500" />
                    Skills & Technologies
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {parsedData.skills.map((skill, index) => (
                      <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-700">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Summary */}
            {parsedData?.summary && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-500" />
                    Professional Summary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 leading-relaxed">{parsedData.summary}</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6">
            {aiAnalysis ? (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="h-5 w-5 text-purple-500" />
                      AI Assessment Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-600">{aiAnalysis.overallScore}/10</div>
                        <div className="text-sm text-gray-600">Overall Score</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-blue-600">{aiAnalysis.recommendation}</div>
                        <div className="text-sm text-gray-600">Recommendation</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">{aiAnalysis.careerLevel}</div>
                        <div className="text-sm text-gray-600">Career Level</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {aiAnalysis.criteria && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Detailed Criteria Assessment</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {aiAnalysis.criteria.map((criterion: any, index: number) => (
                        <div key={index} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{criterion.name}</span>
                            <Badge className={getScoreColor(criterion.score)}>
                              {criterion.score}/10
                            </Badge>
                          </div>
                          <Progress value={criterion.score * 10} className="h-2" />
                          <p className="text-sm text-gray-600">{criterion.justification}</p>
                          {criterion.strengths && criterion.strengths.length > 0 && (
                            <div className="text-xs">
                              <span className="font-medium text-green-600">Strengths: </span>
                              {criterion.strengths.join(', ')}
                            </div>
                          )}
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}

                {aiAnalysis.summary && (
                  <Card>
                    <CardHeader>
                      <CardTitle>AI Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-700 leading-relaxed">{aiAnalysis.summary}</p>
                    </CardContent>
                  </Card>
                )}
              </>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No AI analysis available for this candidate</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="ratings" className="space-y-6">
            {candidate.ratings && candidate.ratings.length > 0 ? (
              <div className="space-y-4">
                {candidate.ratings.map((rating) => (
                  <Card key={rating.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{rating.criteria.name}</CardTitle>
                          <CardDescription>
                            Rated by {rating.interviewer.name} 
                            {rating.interviewer.role && ` (${rating.interviewer.role})`}
                          </CardDescription>
                        </div>
                        <Badge className={getScoreColor(rating.score)}>
                          {rating.score}/{rating.criteria.maxScore}
                        </Badge>
                      </div>
                    </CardHeader>
                    {rating.notes && (
                      <CardContent>
                        <p className="text-sm text-gray-700">{rating.notes}</p>
                        <p className="text-xs text-gray-500 mt-2">
                          {formatDate(rating.createdAt)}
                        </p>
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No ratings available for this candidate</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="documents" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  Documents & Files
                </CardTitle>
              </CardHeader>
              <CardContent>
                {candidate.cvUrl ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-blue-500" />
                        <div>
                          <p className="font-medium">Curriculum Vitae</p>
                          <p className="text-sm text-gray-600">Uploaded CV document</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <a href={candidate.cvUrl} target="_blank" rel="noopener noreferrer">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </a>
                      </Button>
                    </div>
                    
                    {candidate.cvText && (
                      <div className="mt-4">
                        <h4 className="font-medium mb-2">Extracted Text Preview:</h4>
                        <div className="bg-gray-50 p-4 rounded-lg max-h-40 overflow-y-auto">
                          <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                            {candidate.cvText.substring(0, 500)}
                            {candidate.cvText.length > 500 && '...'}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No documents uploaded for this candidate</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
