import { z } from 'zod';

// Candidate validation schemas
export const candidateCreateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email format'),
  phone: z.string().optional(),
  age: z.number().int().min(16, 'Age must be at least 16').max(80, 'Age must be less than 80').optional(),
  experience: z.string().optional(),
  cvText: z.string().optional(),
  cvUrl: z.string().optional(),
  parsedData: z.any().optional()
});

export const candidateUpdateSchema = candidateCreateSchema.partial();

// Interviewer validation schemas
export const interviewerCreateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email format'),
  role: z.string().optional()
});

export const interviewerUpdateSchema = interviewerCreateSchema.partial();

// Criteria validation schemas
export const criteriaCreateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  description: z.string().optional(),
  weight: z.number().min(0.1, 'Weight must be at least 0.1').max(10, 'Weight must be less than 10'),
  maxScore: z.number().int().min(1, 'Max score must be at least 1').max(100, 'Max score must be less than 100')
});

export const criteriaUpdateSchema = criteriaCreateSchema.partial();

// Rating validation schemas
export const ratingCreateSchema = z.object({
  score: z.number().int().min(0, 'Score must be at least 0').max(10, 'Score must be less than or equal to 10'),
  notes: z.string().optional(),
  candidateId: z.string().cuid('Invalid candidate ID'),
  interviewerId: z.string().cuid('Invalid interviewer ID'),
  criteriaId: z.string().cuid('Invalid criteria ID')
});

// File upload validation
export const fileUploadSchema = z.object({
  file: z.any().refine((file) => file instanceof File, 'File is required'),
  candidateId: z.string().cuid('Invalid candidate ID').optional()
});

// AI analysis validation
export const aiAnalysisSchema = z.object({
  candidateId: z.string().cuid('Invalid candidate ID'),
  cvText: z.string().min(10, 'CV text must be at least 10 characters')
});

// Query parameter validation
export const paginationSchema = z.object({
  page: z.string().transform(val => parseInt(val)).pipe(z.number().int().min(1)).optional().default('1'),
  limit: z.string().transform(val => parseInt(val)).pipe(z.number().int().min(1).max(100)).optional().default('10'),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
});

export const candidateFilterSchema = z.object({
  search: z.string().optional(),
  hasCV: z.string().transform(val => val === 'true').optional(),
  hasAIScore: z.string().transform(val => val === 'true').optional(),
  minScore: z.string().transform(val => parseFloat(val)).pipe(z.number().min(0).max(10)).optional(),
  maxScore: z.string().transform(val => parseFloat(val)).pipe(z.number().min(0).max(10)).optional(),
  experience: z.string().optional()
});

// Validation helper functions
export function validateRequest<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      return { success: false, errors };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function validateQueryParams<T>(schema: z.ZodSchema<T>, searchParams: URLSearchParams): { success: true; data: T } | { success: false; errors: string[] } {
  const params: Record<string, string> = {};
  searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return validateRequest(schema, params);
}

// Error response helper
export function createErrorResponse(message: string, errors?: string[], status: number = 400) {
  return {
    error: message,
    errors,
    timestamp: new Date().toISOString()
  };
}

// Success response helper
export function createSuccessResponse<T>(data: T, message?: string) {
  return {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString()
  };
}

// File validation
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

export function validateFileSize(file: File, maxSizeInBytes: number): boolean {
  return file.size <= maxSizeInBytes;
}

export const ALLOWED_CV_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export function validateCVFile(file: File): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!validateFileType(file, ALLOWED_CV_TYPES)) {
    errors.push('Invalid file type. Only PDF, DOC, DOCX, and TXT files are allowed.');
  }

  if (!validateFileSize(file, MAX_FILE_SIZE)) {
    errors.push(`File size too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
