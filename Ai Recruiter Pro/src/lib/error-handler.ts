import { NextResponse } from 'next/server';
import { Prisma } from '@prisma/client';
import { ZodError } from 'zod';
import { logger } from './logger';

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  public errors: string[];

  constructor(message: string, errors: string[] = []) {
    super(message, 400);
    this.errors = errors;
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} not found`, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401);
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429);
  }
}

// Error handler for API routes
export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);

  // Handle known application errors
  if (error instanceof AppError) {
    const response = {
      error: error.message,
      statusCode: error.statusCode,
      timestamp: new Date().toISOString()
    };

    if (error instanceof ValidationError) {
      return NextResponse.json({
        ...response,
        errors: error.errors
      }, { status: error.statusCode });
    }

    return NextResponse.json(response, { status: error.statusCode });
  }

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    return handlePrismaError(error);
  }

  if (error instanceof Prisma.PrismaClientValidationError) {
    return NextResponse.json({
      error: 'Database validation error',
      message: 'Invalid data provided',
      timestamp: new Date().toISOString()
    }, { status: 400 });
  }

  // Handle other known errors
  if (error instanceof Error) {
    // Don't expose internal error messages in production
    const message = process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message;

    return NextResponse.json({
      error: message,
      statusCode: 500,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }

  // Handle unknown errors
  return NextResponse.json({
    error: 'Unknown error occurred',
    statusCode: 500,
    timestamp: new Date().toISOString()
  }, { status: 500 });
}

function handlePrismaError(error: Prisma.PrismaClientKnownRequestError): NextResponse {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target as string[] | undefined;
      const fieldName = field?.[0] || 'field';
      return NextResponse.json({
        error: `${fieldName} already exists`,
        code: error.code,
        timestamp: new Date().toISOString()
      }, { status: 409 });

    case 'P2025':
      // Record not found
      return NextResponse.json({
        error: 'Record not found',
        code: error.code,
        timestamp: new Date().toISOString()
      }, { status: 404 });

    case 'P2003':
      // Foreign key constraint violation
      return NextResponse.json({
        error: 'Related record not found',
        code: error.code,
        timestamp: new Date().toISOString()
      }, { status: 400 });

    case 'P2014':
      // Required relation violation
      return NextResponse.json({
        error: 'Cannot delete record due to related data',
        code: error.code,
        timestamp: new Date().toISOString()
      }, { status: 400 });

    default:
      return NextResponse.json({
        error: 'Database error',
        code: error.code,
        timestamp: new Date().toISOString()
      }, { status: 500 });
  }
}

// Async error wrapper for API routes
export function asyncHandler(fn: Function) {
  return async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Re-export logger for backward compatibility
export { logger as Logger } from './logger';

// Rate limiting utility (simple in-memory implementation)
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private windowMs: number;
  private maxRequests: number;

  constructor(windowMs: number = 60000, maxRequests: number = 100) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;

    // Get existing requests for this identifier
    const requests = this.requests.get(identifier) || [];

    // Filter out old requests
    const recentRequests = requests.filter(time => time > windowStart);

    // Check if limit exceeded
    if (recentRequests.length >= this.maxRequests) {
      return false;
    }

    // Add current request
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);

    return true;
  }

  reset(identifier: string) {
    this.requests.delete(identifier);
  }
}

export const rateLimiter = new RateLimiter();

// Request ID generator for tracing
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
