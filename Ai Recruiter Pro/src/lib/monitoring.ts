import { db } from './db';
import { logger } from './logger';

export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  checks: {
    database: HealthCheckResult;
    memory: HealthCheckResult;
    disk: HealthCheckResult;
    api: HealthCheckResult;
  };
  version: string;
  environment: string;
  uptime: number;
}

export interface HealthCheckResult {
  status: 'pass' | 'fail' | 'warn';
  message: string;
  duration?: number;
  details?: any;
}

export interface PerformanceMetrics {
  timestamp: string;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
  requests: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
  };
  database: {
    connections: number;
    queryTime: number;
  };
}

class MonitoringService {
  private startTime: number;
  private requestMetrics: Map<string, number[]> = new Map();
  private performanceHistory: PerformanceMetrics[] = [];

  constructor() {
    this.startTime = Date.now();
  }

  // Health check methods
  async checkDatabase(): Promise<HealthCheckResult> {
    const start = Date.now();
    try {
      await db.$queryRaw`SELECT 1`;
      const duration = Date.now() - start;
      
      if (duration > 1000) {
        return {
          status: 'warn',
          message: 'Database responding slowly',
          duration,
          details: { threshold: '1000ms', actual: `${duration}ms` }
        };
      }
      
      return {
        status: 'pass',
        message: 'Database connection healthy',
        duration
      };
    } catch (error) {
      logger.error('Database health check failed', error);
      return {
        status: 'fail',
        message: 'Database connection failed',
        duration: Date.now() - start,
        details: error
      };
    }
  }

  checkMemory(): HealthCheckResult {
    const memUsage = process.memoryUsage();
    const totalMem = memUsage.heapTotal;
    const usedMem = memUsage.heapUsed;
    const percentage = (usedMem / totalMem) * 100;

    if (percentage > 90) {
      return {
        status: 'fail',
        message: 'Memory usage critical',
        details: { percentage: `${percentage.toFixed(2)}%`, used: usedMem, total: totalMem }
      };
    }

    if (percentage > 75) {
      return {
        status: 'warn',
        message: 'Memory usage high',
        details: { percentage: `${percentage.toFixed(2)}%`, used: usedMem, total: totalMem }
      };
    }

    return {
      status: 'pass',
      message: 'Memory usage normal',
      details: { percentage: `${percentage.toFixed(2)}%`, used: usedMem, total: totalMem }
    };
  }

  checkDisk(): HealthCheckResult {
    // Simple disk check - in production, you might want to use a more sophisticated method
    try {
      const fs = require('fs');
      const stats = fs.statSync('./');
      
      return {
        status: 'pass',
        message: 'Disk access healthy',
        details: { accessible: true }
      };
    } catch (error) {
      return {
        status: 'fail',
        message: 'Disk access failed',
        details: error
      };
    }
  }

  checkAPI(): HealthCheckResult {
    const recentRequests = this.getRecentRequestMetrics();
    const errorRate = recentRequests.errorRate;

    if (errorRate > 50) {
      return {
        status: 'fail',
        message: 'High API error rate',
        details: { errorRate: `${errorRate.toFixed(2)}%` }
      };
    }

    if (errorRate > 10) {
      return {
        status: 'warn',
        message: 'Elevated API error rate',
        details: { errorRate: `${errorRate.toFixed(2)}%` }
      };
    }

    return {
      status: 'pass',
      message: 'API performance normal',
      details: { 
        errorRate: `${errorRate.toFixed(2)}%`,
        averageResponseTime: `${recentRequests.averageResponseTime}ms`
      }
    };
  }

  async performHealthCheck(): Promise<HealthCheck> {
    const checks = {
      database: await this.checkDatabase(),
      memory: this.checkMemory(),
      disk: this.checkDisk(),
      api: this.checkAPI()
    };

    // Determine overall status
    const hasFailures = Object.values(checks).some(check => check.status === 'fail');
    const hasWarnings = Object.values(checks).some(check => check.status === 'warn');

    let status: 'healthy' | 'unhealthy' | 'degraded';
    if (hasFailures) {
      status = 'unhealthy';
    } else if (hasWarnings) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    return {
      status,
      timestamp: new Date().toISOString(),
      checks,
      version: process.env.npm_package_version || '0.1.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: Date.now() - this.startTime
    };
  }

  // Performance monitoring
  recordRequest(endpoint: string, method: string, statusCode: number, responseTime: number): void {
    const key = `${method}:${endpoint}`;
    
    if (!this.requestMetrics.has(key)) {
      this.requestMetrics.set(key, []);
    }
    
    const metrics = this.requestMetrics.get(key)!;
    metrics.push(responseTime);
    
    // Keep only last 100 requests per endpoint
    if (metrics.length > 100) {
      metrics.shift();
    }

    // Log slow requests
    if (responseTime > 5000) {
      logger.warn(`Slow request detected: ${key} took ${responseTime}ms`);
    }

    // Log errors
    if (statusCode >= 400) {
      logger.error(`Request failed: ${key} returned ${statusCode}`);
    }
  }

  getRecentRequestMetrics(): { errorRate: number; averageResponseTime: number; totalRequests: number } {
    let totalRequests = 0;
    let totalResponseTime = 0;
    let errorCount = 0;

    for (const [endpoint, times] of this.requestMetrics.entries()) {
      totalRequests += times.length;
      totalResponseTime += times.reduce((sum, time) => sum + time, 0);
      
      // Assume errors are recorded as negative response times (hack for this example)
      errorCount += times.filter(time => time < 0).length;
    }

    return {
      errorRate: totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0,
      averageResponseTime: totalRequests > 0 ? Math.round(totalResponseTime / totalRequests) : 0,
      totalRequests
    };
  }

  collectPerformanceMetrics(): PerformanceMetrics {
    const memUsage = process.memoryUsage();
    const requestMetrics = this.getRecentRequestMetrics();

    const metrics: PerformanceMetrics = {
      timestamp: new Date().toISOString(),
      memory: {
        used: memUsage.heapUsed,
        total: memUsage.heapTotal,
        percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100
      },
      cpu: {
        usage: process.cpuUsage().user / 1000000 // Convert to seconds
      },
      requests: {
        total: requestMetrics.totalRequests,
        successful: requestMetrics.totalRequests - (requestMetrics.errorRate / 100 * requestMetrics.totalRequests),
        failed: requestMetrics.errorRate / 100 * requestMetrics.totalRequests,
        averageResponseTime: requestMetrics.averageResponseTime
      },
      database: {
        connections: 1, // SQLite doesn't have connection pooling
        queryTime: 0 // Would need to implement query time tracking
      }
    };

    // Store in history (keep last 100 entries)
    this.performanceHistory.push(metrics);
    if (this.performanceHistory.length > 100) {
      this.performanceHistory.shift();
    }

    return metrics;
  }

  getPerformanceHistory(): PerformanceMetrics[] {
    return this.performanceHistory;
  }

  // Alert system
  checkAlerts(): string[] {
    const alerts: string[] = [];
    const metrics = this.collectPerformanceMetrics();

    if (metrics.memory.percentage > 90) {
      alerts.push(`Critical: Memory usage at ${metrics.memory.percentage.toFixed(2)}%`);
    }

    if (metrics.requests.averageResponseTime > 5000) {
      alerts.push(`Warning: Average response time is ${metrics.requests.averageResponseTime}ms`);
    }

    const errorRate = (metrics.requests.failed / metrics.requests.total) * 100;
    if (errorRate > 10) {
      alerts.push(`Warning: Error rate is ${errorRate.toFixed(2)}%`);
    }

    return alerts;
  }
}

// Create singleton instance
export const monitoring = new MonitoringService();

// Export for use in other modules
export default monitoring;
