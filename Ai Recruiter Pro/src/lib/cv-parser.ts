import pdf from 'pdf-parse';
import mammoth from 'mammoth';

export interface ParsedCVData {
  name?: string;
  email?: string;
  phone?: string;
  age?: number;
  experience?: string;
  skills?: string[];
  education?: string;
  currentRole?: string;
  summary?: string;
  location?: string;
  linkedin?: string;
  github?: string;
}

export class CVParser {
  /**
   * Extract text from PDF file
   */
  static async extractTextFromPDF(buffer: Buffer): Promise<string> {
    try {
      const data = await pdf(buffer);
      return data.text;
    } catch (error) {
      console.error('Error extracting text from PDF:', error);
      throw new Error('Failed to extract text from PDF');
    }
  }

  /**
   * Extract text from Word document
   */
  static async extractTextFromWord(buffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } catch (error) {
      console.error('Error extracting text from Word document:', error);
      throw new Error('Failed to extract text from Word document');
    }
  }

  /**
   * Extract text from file based on MIME type
   */
  static async extractTextFromFile(buffer: Buffer, mimeType: string): Promise<string> {
    switch (mimeType) {
      case 'application/pdf':
        return this.extractTextFromPDF(buffer);
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.extractTextFromWord(buffer);
      case 'text/plain':
        return buffer.toString('utf-8');
      default:
        throw new Error(`Unsupported file type: ${mimeType}`);
    }
  }

  /**
   * Parse CV text using regex patterns
   */
  static parseWithRegex(text: string): Partial<ParsedCVData> {
    const parsed: Partial<ParsedCVData> = {};

    // Email extraction
    const emailMatch = text.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/);
    if (emailMatch) {
      parsed.email = emailMatch[0];
    }

    // Phone extraction (various formats)
    const phoneMatch = text.match(/(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/);
    if (phoneMatch) {
      parsed.phone = phoneMatch[0];
    }

    // LinkedIn extraction
    const linkedinMatch = text.match(/(?:linkedin\.com\/in\/|linkedin\.com\/profile\/view\?id=)([a-zA-Z0-9-]+)/);
    if (linkedinMatch) {
      parsed.linkedin = `https://linkedin.com/in/${linkedinMatch[1]}`;
    }

    // GitHub extraction
    const githubMatch = text.match(/(?:github\.com\/)([a-zA-Z0-9-]+)/);
    if (githubMatch) {
      parsed.github = `https://github.com/${githubMatch[1]}`;
    }

    // Experience extraction (years)
    const experienceMatch = text.match(/(\d+)[\s]*(?:years?|yrs?)[\s]*(?:of[\s]*)?(?:experience|exp)/i);
    if (experienceMatch) {
      parsed.experience = `${experienceMatch[1]} years`;
    }

    // Age extraction
    const ageMatch = text.match(/(?:age|born)[\s:]*(\d{1,2})/i);
    if (ageMatch) {
      parsed.age = parseInt(ageMatch[1]);
    }

    // Skills extraction (common programming languages and technologies)
    const skillsPattern = /\b(?:JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin|React|Angular|Vue|Node\.js|Express|Django|Flask|Spring|Laravel|Docker|Kubernetes|AWS|Azure|GCP|MongoDB|PostgreSQL|MySQL|Redis|Git|Jenkins|CI\/CD|DevOps|Machine Learning|AI|Data Science|HTML|CSS|SASS|LESS|Webpack|Babel|Jest|Cypress|Selenium|Agile|Scrum|REST|GraphQL|API|Microservices|Linux|Windows|macOS|Figma|Sketch|Photoshop|Illustrator)\b/gi;
    const skillsMatches = text.match(skillsPattern);
    if (skillsMatches) {
      parsed.skills = [...new Set(skillsMatches.map(skill => skill.toLowerCase()))];
    }

    // Education extraction
    const educationMatch = text.match(/(?:Bachelor|Master|PhD|B\.S\.|M\.S\.|B\.A\.|M\.A\.|B\.Sc\.|M\.Sc\.)[\s\w]*(?:in[\s]*)?[\w\s]+/i);
    if (educationMatch) {
      parsed.education = educationMatch[0];
    }

    return parsed;
  }

  /**
   * Extract name from text (simple heuristic)
   */
  static extractName(text: string): string | undefined {
    // Look for name patterns at the beginning of the document
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    
    for (let i = 0; i < Math.min(5, lines.length); i++) {
      const line = lines[i].trim();
      
      // Skip lines that look like headers, emails, phones, etc.
      if (line.includes('@') || line.includes('CV') || line.includes('Resume') || 
          line.includes('Phone') || line.includes('Email') || line.length < 3) {
        continue;
      }
      
      // Look for name pattern (2-4 words, each starting with capital letter)
      const nameMatch = line.match(/^([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})$/);
      if (nameMatch) {
        return nameMatch[1];
      }
    }

    return undefined;
  }

  /**
   * Complete CV parsing with both regex and AI enhancement
   */
  static async parseCV(buffer: Buffer, mimeType: string): Promise<{ text: string; parsed: ParsedCVData }> {
    // Extract text from file
    const text = await this.extractTextFromFile(buffer, mimeType);
    
    // Parse with regex
    const regexParsed = this.parseWithRegex(text);
    
    // Extract name
    const name = this.extractName(text);
    
    // Combine results
    const parsed: ParsedCVData = {
      name,
      ...regexParsed
    };

    return { text, parsed };
  }

  /**
   * Validate parsed data
   */
  static validateParsedData(parsed: ParsedCVData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!parsed.name || parsed.name.length < 2) {
      errors.push('Name is required and must be at least 2 characters');
    }

    if (parsed.email && !parsed.email.includes('@')) {
      errors.push('Invalid email format');
    }

    if (parsed.age && (parsed.age < 16 || parsed.age > 80)) {
      errors.push('Age must be between 16 and 80');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate fallback data for failed parsing
   */
  static generateFallbackData(fileName: string): ParsedCVData {
    return {
      name: fileName.replace(/\.[^/.]+$/, "").replace(/[-_]/g, " "),
      email: `${fileName.toLowerCase().replace(/\.[^/.]+$/, "").replace(/[^a-z0-9]/g, "")}@example.com`,
      experience: "Not specified",
      skills: [],
      education: "Not specified"
    };
  }
}
