import { Server } from 'socket.io';

interface InterviewerPresence {
  id: string;
  name: string;
  socketId: string;
  lastSeen: Date;
}

const activeInterviewers = new Map<string, InterviewerPresence>();

export const setupSocket = (io: Server) => {
  io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);
    
    // <PERSON>le interviewer joining
    socket.on('join-interview-session', (data: { interviewerId: string; interviewerName: string }) => {
      const { interviewerId, interviewerName } = data;
      
      // Add to active interviewers
      activeInterviewers.set(interviewerId, {
        id: interviewerId,
        name: interviewerName,
        socketId: socket.id,
        lastSeen: new Date()
      });

      // Join interviewer to a room for candidate updates
      socket.join('interviewers');
      
      // Notify all interviewers about the new joiner
      io.to('interviewers').emit('interviewer-joined', {
        interviewerId,
        interviewerName,
        activeInterviewers: Array.from(activeInterviewers.values())
      });

      // Send current active interviewers list to the new interviewer
      socket.emit('active-interviewers', Array.from(activeInterviewers.values()));
    });

    // Handle rating submission
    socket.on('rating-submitted', (data: {
      candidateId: string;
      candidateName: string;
      interviewerId: string;
      interviewerName: string;
      score: number;
      criteriaName: string;
    }) => {
      // Broadcast rating update to all interviewers
      io.to('interviewers').emit('rating-update', {
        ...data,
        timestamp: new Date().toISOString()
      });

      // Also trigger candidate score recalculation notification
      io.emit('candidate-score-updated', {
        candidateId: data.candidateId,
        candidateName: data.candidateName,
        message: `${data.interviewerName} submitted a rating for ${data.candidateName}`
      });
    });

    // Handle candidate CV upload
    socket.on('cv-uploaded', (data: {
      candidateId: string;
      candidateName: string;
      interviewerName: string;
    }) => {
      io.to('interviewers').emit('cv-upload-notification', {
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle CV parsing completion
    socket.on('cv-parsed', (data: {
      candidateId: string;
      candidateName: string;
      extractedInfo: string;
      interviewerName: string;
    }) => {
      io.to('interviewers').emit('cv-parsed', {
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle AI analysis completion
    socket.on('ai-analysis-completed', (data: {
      candidateId: string;
      candidateName: string;
      aiScore: number;
      interviewerName: string;
    }) => {
      io.to('interviewers').emit('ai-analysis-update', {
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle ping for presence
    socket.on('ping', (data: { interviewerId: string }) => {
      const interviewer = activeInterviewers.get(data.interviewerId);
      if (interviewer) {
        interviewer.lastSeen = new Date();
        activeInterviewers.set(data.interviewerId, interviewer);
      }
    });

    // Handle request for latest candidate data
    socket.on('request-candidate-update', (data: { candidateId: string }) => {
      // This would typically fetch latest data from database and emit back
      // For now, we'll just acknowledge the request
      socket.emit('candidate-update-requested', {
        candidateId: data.candidateId,
        timestamp: new Date().toISOString()
      });
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
      
      // Remove from active interviewers
      for (const [interviewerId, interviewer] of activeInterviewers.entries()) {
        if (interviewer.socketId === socket.id) {
          activeInterviewers.delete(interviewerId);
          
          // Notify other interviewers
          io.to('interviewers').emit('interviewer-left', {
            interviewerId,
            interviewerName: interviewer.name,
            activeInterviewers: Array.from(activeInterviewers.values())
          });
          break;
        }
      }
    });

    // Send welcome message
    socket.emit('message', {
      text: 'Welcome to Interview Selection System Real-time Collaboration!',
      senderId: 'system',
      timestamp: new Date().toISOString(),
    });
  });

  // Clean up inactive interviewers every minute
  setInterval(() => {
    const now = new Date();
    const inactiveThreshold = 5 * 60 * 1000; // 5 minutes
    
    for (const [interviewerId, interviewer] of activeInterviewers.entries()) {
      if (now.getTime() - interviewer.lastSeen.getTime() > inactiveThreshold) {
        activeInterviewers.delete(interviewerId);
        io.to('interviewers').emit('interviewer-left', {
          interviewerId,
          interviewerName: interviewer.name,
          activeInterviewers: Array.from(activeInterviewers.values())
        });
      }
    }
  }, 60000); // Check every minute
};

// Helper functions to emit events from outside socket context
export const emitRatingUpdate = (io: Server, ratingData: any) => {
  io.to('interviewers').emit('rating-update', {
    ...ratingData,
    timestamp: new Date().toISOString()
  });
};

export const emitCandidateScoreUpdate = (io: Server, candidateData: any) => {
  io.emit('candidate-score-updated', {
    ...candidateData,
    timestamp: new Date().toISOString()
  });
};