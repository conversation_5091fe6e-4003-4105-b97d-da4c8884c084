import { NextRequest } from 'next/server';
import { logger } from './logger';

export interface SecurityConfig {
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  cors: {
    origin: string[];
    methods: string[];
    allowedHeaders: string[];
  };
  fileUpload: {
    maxSize: number;
    allowedTypes: string[];
    allowedExtensions: string[];
  };
  headers: {
    contentSecurityPolicy: string;
    strictTransportSecurity: string;
  };
}

export const securityConfig: SecurityConfig = {
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'), // 1 minute
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  },
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  },
  fileUpload: {
    maxSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    allowedExtensions: ['.pdf', '.doc', '.docx'],
  },
  headers: {
    contentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' ws: wss:;",
    strictTransportSecurity: 'max-age=31536000; includeSubDomains; preload',
  },
};

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

export function sanitizeObject(obj: any): any {
  if (typeof obj === 'string') {
    return sanitizeInput(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[sanitizeInput(key)] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}

// SQL injection prevention
export function validateSQLInput(input: string): boolean {
  const sqlInjectionPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /('|(\\')|(;)|(--)|(\|)|(\*)|(%)|(\+))/,
    /((\%3C)|<)((\%2F)|\/)*[a-z0-9\%]+((\%3E)|>)/i,
    /((\%3C)|<)((\%69)|i|(\%49))((\%6D)|m|(\%4D))((\%67)|g|(\%47))[^\n]+((\%3E)|>)/i,
  ];
  
  return !sqlInjectionPatterns.some(pattern => pattern.test(input));
}

// XSS prevention
export function validateXSSInput(input: string): boolean {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[\\s]*=[\\s]*["\']javascript:/gi,
  ];
  
  return !xssPatterns.some(pattern => pattern.test(input));
}

// File validation
export function validateFile(file: File): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check file size
  if (file.size > securityConfig.fileUpload.maxSize) {
    errors.push(`File size exceeds maximum allowed size of ${securityConfig.fileUpload.maxSize} bytes`);
  }
  
  // Check file type
  if (!securityConfig.fileUpload.allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }
  
  // Check file extension
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!securityConfig.fileUpload.allowedExtensions.includes(extension)) {
    errors.push(`File extension ${extension} is not allowed`);
  }
  
  // Check for suspicious file names
  const suspiciousPatterns = [
    /\.(exe|bat|cmd|scr|pif|com)$/i,
    /\.(php|asp|jsp|js)$/i,
    /\.\./,
    /[<>:"|?*]/,
  ];
  
  if (suspiciousPatterns.some(pattern => pattern.test(file.name))) {
    errors.push('File name contains suspicious characters or extensions');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Request validation
export function validateRequest(request: NextRequest): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check content length
  const contentLength = request.headers.get('content-length');
  if (contentLength && parseInt(contentLength) > 50 * 1024 * 1024) { // 50MB
    errors.push('Request body too large');
  }
  
  // Check for suspicious headers
  const userAgent = request.headers.get('user-agent');
  if (!userAgent || userAgent.length < 10) {
    errors.push('Invalid or missing user agent');
  }
  
  // Check for common attack patterns in URL
  const url = request.url;
  const suspiciousUrlPatterns = [
    /\.\./,
    /%2e%2e/i,
    /\/etc\/passwd/i,
    /\/proc\/self\/environ/i,
    /<script/i,
    /javascript:/i,
  ];
  
  if (suspiciousUrlPatterns.some(pattern => pattern.test(url))) {
    errors.push('Suspicious URL pattern detected');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// IP-based rate limiting
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  isAllowed(ip: string): boolean {
    const now = Date.now();
    const windowStart = now - securityConfig.rateLimit.windowMs;
    
    if (!this.requests.has(ip)) {
      this.requests.set(ip, []);
    }
    
    const ipRequests = this.requests.get(ip)!;
    
    // Remove old requests outside the window
    const validRequests = ipRequests.filter(timestamp => timestamp > windowStart);
    
    // Check if limit exceeded
    if (validRequests.length >= securityConfig.rateLimit.maxRequests) {
      logger.security('Rate limit exceeded', { ip, requests: validRequests.length });
      return false;
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(ip, validRequests);
    
    return true;
  }
  
  getRemainingRequests(ip: string): number {
    const now = Date.now();
    const windowStart = now - securityConfig.rateLimit.windowMs;
    
    if (!this.requests.has(ip)) {
      return securityConfig.rateLimit.maxRequests;
    }
    
    const ipRequests = this.requests.get(ip)!;
    const validRequests = ipRequests.filter(timestamp => timestamp > windowStart);
    
    return Math.max(0, securityConfig.rateLimit.maxRequests - validRequests.length);
  }
  
  // Cleanup old entries periodically
  cleanup(): void {
    const now = Date.now();
    const windowStart = now - securityConfig.rateLimit.windowMs;
    
    for (const [ip, requests] of this.requests.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > windowStart);
      if (validRequests.length === 0) {
        this.requests.delete(ip);
      } else {
        this.requests.set(ip, validRequests);
      }
    }
  }
}

export const rateLimiter = new RateLimiter();

// Cleanup rate limiter every 5 minutes
setInterval(() => {
  rateLimiter.cleanup();
}, 5 * 60 * 1000);

// Security headers middleware
export function getSecurityHeaders(): Record<string, string> {
  return {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'origin-when-cross-origin',
    'X-XSS-Protection': '1; mode=block',
    'Content-Security-Policy': securityConfig.headers.contentSecurityPolicy,
    'Strict-Transport-Security': securityConfig.headers.strictTransportSecurity,
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  };
}

// Generate secure random string
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export default {
  sanitizeInput,
  sanitizeObject,
  validateSQLInput,
  validateXSSInput,
  validateFile,
  validateRequest,
  rateLimiter,
  getSecurityHeaders,
  generateSecureToken,
  securityConfig,
};
