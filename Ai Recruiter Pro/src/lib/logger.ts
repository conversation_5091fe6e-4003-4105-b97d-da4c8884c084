import { writeFileSync, appendFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  meta?: any;
  requestId?: string;
}

class Logger {
  private logLevel: LogLevel;
  private logFile: string;
  private enableConsole: boolean;

  constructor() {
    this.logLevel = this.getLogLevel();
    this.logFile = process.env.LOG_FILE || './logs/app.log';
    this.enableConsole = process.env.NODE_ENV !== 'production';
    
    // Ensure log directory exists
    this.ensureLogDirectory();
  }

  private getLogLevel(): LogLevel {
    const level = process.env.LOG_LEVEL?.toUpperCase() || 'INFO';
    switch (level) {
      case 'ERROR': return LogLevel.ERROR;
      case 'WARN': return LogLevel.WARN;
      case 'INFO': return LogLevel.INFO;
      case 'DEBUG': return LogLevel.DEBUG;
      default: return LogLevel.INFO;
    }
  }

  private ensureLogDirectory(): void {
    const logDir = this.logFile.substring(0, this.logFile.lastIndexOf('/'));
    if (!existsSync(logDir)) {
      mkdirSync(logDir, { recursive: true });
    }
  }

  private formatLogEntry(level: string, message: string, meta?: any, requestId?: string): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      meta,
      requestId,
    };
  }

  private writeLog(entry: LogEntry): void {
    const logLine = JSON.stringify(entry) + '\n';
    
    // Write to console in development
    if (this.enableConsole) {
      const colorMap = {
        ERROR: '\x1b[31m', // Red
        WARN: '\x1b[33m',  // Yellow
        INFO: '\x1b[36m',  // Cyan
        DEBUG: '\x1b[90m', // Gray
      };
      const color = colorMap[entry.level as keyof typeof colorMap] || '';
      const reset = '\x1b[0m';
      console.log(`${color}[${entry.timestamp}] ${entry.level}: ${entry.message}${reset}`, entry.meta || '');
    }
    
    // Write to file
    try {
      appendFileSync(this.logFile, logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  error(message: string, meta?: any, requestId?: string): void {
    if (this.logLevel >= LogLevel.ERROR) {
      const entry = this.formatLogEntry('ERROR', message, meta, requestId);
      this.writeLog(entry);
    }
  }

  warn(message: string, meta?: any, requestId?: string): void {
    if (this.logLevel >= LogLevel.WARN) {
      const entry = this.formatLogEntry('WARN', message, meta, requestId);
      this.writeLog(entry);
    }
  }

  info(message: string, meta?: any, requestId?: string): void {
    if (this.logLevel >= LogLevel.INFO) {
      const entry = this.formatLogEntry('INFO', message, meta, requestId);
      this.writeLog(entry);
    }
  }

  debug(message: string, meta?: any, requestId?: string): void {
    if (this.logLevel >= LogLevel.DEBUG) {
      const entry = this.formatLogEntry('DEBUG', message, meta, requestId);
      this.writeLog(entry);
    }
  }

  // Request logging helpers
  request(method: string, url: string, requestId: string, meta?: any): void {
    this.info(`${method} ${url}`, meta, requestId);
  }

  response(method: string, url: string, statusCode: number, duration: number, requestId: string): void {
    this.info(`${method} ${url} - ${statusCode} (${duration}ms)`, undefined, requestId);
  }

  // Performance logging
  performance(operation: string, duration: number, meta?: any): void {
    this.info(`Performance: ${operation} completed in ${duration}ms`, meta);
  }

  // Security logging
  security(event: string, details: any, requestId?: string): void {
    this.warn(`Security Event: ${event}`, details, requestId);
  }

  // Database logging
  database(operation: string, table: string, duration?: number, meta?: any): void {
    const message = duration 
      ? `DB: ${operation} on ${table} (${duration}ms)`
      : `DB: ${operation} on ${table}`;
    this.debug(message, meta);
  }

  // AI/ML logging
  ai(operation: string, model: string, duration?: number, meta?: any): void {
    const message = duration 
      ? `AI: ${operation} with ${model} (${duration}ms)`
      : `AI: ${operation} with ${model}`;
    this.info(message, meta);
  }

  // File operation logging
  file(operation: string, filename: string, size?: number, meta?: any): void {
    const message = size 
      ? `File: ${operation} ${filename} (${size} bytes)`
      : `File: ${operation} ${filename}`;
    this.debug(message, meta);
  }

  // Cleanup old logs (call this periodically)
  cleanup(daysToKeep: number = 30): void {
    // This is a simple implementation - in production, you might want a more sophisticated log rotation
    this.info(`Log cleanup initiated - keeping ${daysToKeep} days of logs`);
  }
}

// Create singleton instance
export const logger = new Logger();

// Export for use in other modules
export default logger;
