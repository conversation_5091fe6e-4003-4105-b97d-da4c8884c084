version: '3.8'

services:
  ai-recruiter-pro:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:./db/production.db
      - NEXTAUTH_SECRET=your-production-secret-key-here
      - NEXTAUTH_URL=http://localhost:3000
      - NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
      - Z_AI_API_KEY=your-z-ai-api-key
    volumes:
      - ./db:/app/db
      - ./public/uploads:/app/public/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy (nginx)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ai-recruiter-pro
    restart: unless-stopped

volumes:
  db_data:
  uploads_data:
  logs_data:
