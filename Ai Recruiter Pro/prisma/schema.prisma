// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Candidate {
  id           String   @id @default(cuid())
  name         String
  email        String   @unique
  phone        String?
  age          Int?
  experience   String?
  cvUrl        String?
  cvText       String?
  parsedData   String?  // JSON string for parsed CV data
  aiScore      Float?
  aiAnalysis   String?
  averageScore Float?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  ratings      Rating[]
  sessions     InterviewSessionCandidate[]
}

model Interviewer {
  id        String   @id @default(cuid())
  name      String
  email     String   @unique
  role      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  ratings   Rating[]
  sessions  InterviewSessionInterviewer[]
}

model Criteria {
  id          String   @id @default(cuid())
  name        String
  description String?
  weight      Float    @default(1.0)
  maxScore    Int      @default(10)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  ratings     Rating[]
}

model Rating {
  id          String   @id @default(cuid())
  score       Int
  notes       String?
  candidateId String
  interviewerId String
  criteriaId  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  candidate   Candidate @relation(fields: [candidateId], references: [id], onDelete: Cascade)
  interviewer Interviewer @relation(fields: [interviewerId], references: [id], onDelete: Cascade)
  criteria    Criteria @relation(fields: [criteriaId], references: [id], onDelete: Cascade)
  
  @@unique([candidateId, interviewerId, criteriaId])
}

model InterviewSession {
  id          String   @id @default(cuid())
  name        String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  candidates  InterviewSessionCandidate[]
  interviewers InterviewSessionInterviewer[]
}

model InterviewSessionCandidate {
  sessionId   String
  candidateId String
  createdAt   DateTime @default(now())
  
  session     InterviewSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  candidate   Candidate @relation(fields: [candidateId], references: [id], onDelete: Cascade)
  
  @@id([sessionId, candidateId])
}

model InterviewSessionInterviewer {
  sessionId    String
  interviewerId String
  createdAt    DateTime @default(now())
  
  session      InterviewSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  interviewer  Interviewer @relation(fields: [interviewerId], references: [id], onDelete: Cascade)
  
  @@id([sessionId, interviewerId])
}