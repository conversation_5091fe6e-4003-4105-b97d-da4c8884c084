import { db } from '../src/lib/db'

async function main() {
  // Create default criteria
  const criteriaData = [
    {
      name: 'Technical Skills',
      description: 'Assessment of technical knowledge, programming skills, and technical problem-solving abilities',
      weight: 2.0,
      maxScore: 10
    },
    {
      name: 'Communication Skills',
      description: 'Ability to communicate clearly, explain concepts, and articulate thoughts effectively',
      weight: 1.5,
      maxScore: 10
    },
    {
      name: 'Problem Solving',
      description: 'Approach to solving problems, analytical thinking, and creativity in finding solutions',
      weight: 2.0,
      maxScore: 10
    },
    {
      name: 'Team Collaboration',
      description: 'Ability to work well in a team, interpersonal skills, and collaborative mindset',
      weight: 1.5,
      maxScore: 10
    },
    {
      name: 'Cultural Fit',
      description: 'Alignment with company values, work ethic, and overall cultural compatibility',
      weight: 1.0,
      maxScore: 10
    },
    {
      name: 'Experience & Background',
      description: 'Relevant work experience, education, and project background',
      weight: 2.0,
      maxScore: 10
    }
  ]

  // First, let's clear existing data to avoid conflicts
  await db.rating.deleteMany()
  await db.candidate.deleteMany()
  await db.interviewer.deleteMany()
  await db.criteria.deleteMany()

  for (const criteria of criteriaData) {
    await db.criteria.create({
      data: criteria
    })
  }

  // Create default interviewers
  const interviewersData = [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Technical Lead'
    },
    {
      name: 'Michael Chen',
      email: '<EMAIL>',
      role: 'Engineering Manager'
    },
    {
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      role: 'Senior Developer'
    }
  ]

  for (const interviewer of interviewersData) {
    await db.interviewer.create({
      data: interviewer
    })
  }

  // Create sample candidates
  const candidatesData = [
    {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '******-0123',
      age: 28,
      experience: '5 years',
      cvText: JSON.stringify({
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0123',
        age: 28,
        experience: '5 years',
        skills: ['React', 'Node.js', 'Python', 'JavaScript', 'TypeScript'],
        education: 'Bachelor of Science in Computer Science'
      }),
      parsedData: JSON.stringify({
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0123',
        age: 28,
        experience: '5 years',
        skills: ['React', 'Node.js', 'Python', 'JavaScript', 'TypeScript'],
        education: 'Bachelor of Science in Computer Science'
      })
    },
    {
      name: 'Jane Doe',
      email: '<EMAIL>',
      phone: '******-0124',
      age: 24,
      experience: '2 years',
      cvText: JSON.stringify({
        name: 'Jane Doe',
        email: '<EMAIL>',
        phone: '******-0124',
        age: 24,
        experience: '2 years',
        skills: ['JavaScript', 'React', 'CSS', 'HTML', 'Git'],
        education: 'Bachelor of Science in Software Engineering'
      }),
      parsedData: JSON.stringify({
        name: 'Jane Doe',
        email: '<EMAIL>',
        phone: '******-0124',
        age: 24,
        experience: '2 years',
        skills: ['JavaScript', 'React', 'CSS', 'HTML', 'Git'],
        education: 'Bachelor of Science in Software Engineering'
      })
    },
    {
      name: 'Alex Thompson',
      email: '<EMAIL>',
      phone: '******-0125',
      age: 32,
      experience: '8 years',
      cvText: JSON.stringify({
        name: 'Alex Thompson',
        email: '<EMAIL>',
        phone: '******-0125',
        age: 32,
        experience: '8 years',
        skills: ['DevOps', 'AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Python'],
        education: 'Master of Science in Computer Engineering'
      }),
      parsedData: JSON.stringify({
        name: 'Alex Thompson',
        email: '<EMAIL>',
        phone: '******-0125',
        age: 32,
        experience: '8 years',
        skills: ['DevOps', 'AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Python'],
        education: 'Master of Science in Computer Engineering'
      })
    }
  ]

  for (const candidate of candidatesData) {
    await db.candidate.create({
      data: candidate
    })
  }

  console.log('Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await db.$disconnect()
  })